{"info": {"name": "ChatApp Phase 3 - End-to-End Encryption API", "description": "Complete API collection for testing encrypted messaging functionality including REST API key management and WebSocket real-time messaging.", "version": "3.0.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{jwt_token}}", "type": "string"}]}, "variable": [{"key": "base_url", "value": "http://127.0.0.1:8000", "type": "string"}, {"key": "socket_url", "value": "ws://127.0.0.1:3001", "type": "string"}, {"key": "jwt_token", "value": "", "type": "string"}, {"key": "user_id", "value": "", "type": "string"}, {"key": "conversation_id", "value": "", "type": "string"}, {"key": "target_user_id", "value": "", "type": "string"}], "item": [{"name": "Authentication", "item": [{"name": "Login User", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('jwt_token', response.access);", "    pm.collectionVariables.set('user_id', response.user.id);", "    console.log('JWT Token set:', response.access);", "    console.log('User ID set:', response.user.id);", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"testpass123\"\n}"}, "url": {"raw": "{{base_url}}/api/auth/login/", "host": ["{{base_url}}"], "path": ["api", "auth", "login", ""]}}}, {"name": "Register User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"alice_test\",\n  \"email\": \"<EMAIL>\",\n  \"password\": \"testpass123\",\n  \"first_name\": \"<PERSON>\",\n  \"last_name\": \"Test\"\n}"}, "url": {"raw": "{{base_url}}/api/auth/register/", "host": ["{{base_url}}"], "path": ["api", "auth", "register", ""]}}}]}, {"name": "Encryption Key Management", "item": [{"name": "Upload Key Bundle", "event": [{"listen": "test", "script": {"exec": ["pm.test('Key bundle uploaded successfully', function () {", "    pm.response.to.have.status(201);", "    const response = pm.response.json();", "    pm.expect(response).to.have.property('key_bundle_id');", "    pm.expect(response).to.have.property('message');", "});"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"identity_public_key\": \"MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEVkpOnGmFntib9F/I6tIQmWeEDO186HPnDEKbTtMfiqAslpRFuBc6vvenIetWqyYJu/hXt25NdcJFoVAk2yr6zw==\",\n  \"signed_prekey_id\": 1,\n  \"signed_prekey_public\": \"MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAE9o1LVOhahkUcLCAZgtF/2YpK8V7brrv0j5Au+B95HJ4lyDWAPMZfoEYJFYyE+7PaFG+lxo2B/jBch0aeTlI3dQ==\",\n  \"signed_prekey_signature\": \"MEUCIF4z5ltcLiOHEBq8ykORetLXOh4oD3yZjGtCKu8Rv/QSAiEAjK8Y4pOJYSGOPEX65j2XLK9IgkMjC7zRXMtUAvrUxEQ=\"\n}"}, "url": {"raw": "{{base_url}}/api/encryption/bundles/", "host": ["{{base_url}}"], "path": ["api", "encryption", "bundles", ""]}}}, {"name": "Get Key Bundle", "event": [{"listen": "test", "script": {"exec": ["pm.test('Key bundle retrieved successfully', function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response).to.have.property('identity_public_key');", "    pm.expect(response).to.have.property('signed_prekey');", "});"]}}], "request": {"method": "GET", "url": {"raw": "{{base_url}}/api/encryption/bundles/{{target_user_id}}/", "host": ["{{base_url}}"], "path": ["api", "encryption", "bundles", "{{target_user_id}}", ""]}}}, {"name": "Upload One-Time Pre-Keys", "event": [{"listen": "test", "script": {"exec": ["pm.test('One-time pre-keys uploaded successfully', function () {", "    pm.response.to.have.status(201);", "    const response = pm.response.json();", "    pm.expect(response).to.have.property('count');", "    pm.expect(response.count).to.equal(5);", "});"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"prekeys\": [\n    {\n      \"key_id\": 1,\n      \"public_key\": \"MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAE5yrcY99xmEbnRvXvZriCLjc2SyoGxrGuE23HNMNU4bEqwdg0f3Tf7Clf4kmYzQ9gummdQszpsLOsE+JVZs4JKw==\"\n    },\n    {\n      \"key_id\": 2,\n      \"public_key\": \"MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEyZ6+hgPsQd+SYQr3ftgZEeDI11y2c+kXfVUNgoOmSq8Rc+Y3bSFsNnQCzpUiYlAbiIlV7V/RFsolUfsny/yCSQ==\"\n    },\n    {\n      \"key_id\": 3,\n      \"public_key\": \"MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEpQr8kV7brrv0j5Au+B95HJ4lyDWAPMZfoEYJFYyE+7PaFG+lxo2B/jBch0aeTlI3dQyZ6+hgPsQd+SYQr3ftgZ==\"\n    },\n    {\n      \"key_id\": 4,\n      \"public_key\": \"MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEeDI11y2c+kXfVUNgoOmSq8Rc+Y3bSFsNnQCzpUiYlAbiIlV7V/RFsolUfsny/yCSQyrcY99xmEbnRvXvZriCLj==\"\n    },\n    {\n      \"key_id\": 5,\n      \"public_key\": \"MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEc2SyoGxrGuE23HNMNU4bEqwdg0f3Tf7Clf4kmYzQ9gummdQszpsLOsE+JVZs4JKwlpRFuBc6vvenIetWqyYJu/hX==\"\n    }\n  ]\n}"}, "url": {"raw": "{{base_url}}/api/encryption/prekeys/", "host": ["{{base_url}}"], "path": ["api", "encryption", "prekeys", ""]}}}, {"name": "Get Pre-Key Count", "event": [{"listen": "test", "script": {"exec": ["pm.test('Pre-key count retrieved successfully', function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response).to.have.property('available_count');", "    pm.expect(response).to.have.property('total_count');", "    pm.expect(response).to.have.property('used_count');", "});"]}}], "request": {"method": "GET", "url": {"raw": "{{base_url}}/api/encryption/prekeys/count/", "host": ["{{base_url}}"], "path": ["api", "encryption", "prekeys", "count", ""]}}}]}, {"name": "Messaging API", "item": [{"name": "List Conversations", "event": [{"listen": "test", "script": {"exec": ["pm.test('Conversations retrieved successfully', function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response).to.have.property('results');", "    pm.expect(response.results).to.be.an('array');", "    if (response.results.length > 0) {", "        const conversation = response.results[0];", "        pm.expect(conversation).to.have.property('id');", "        pm.expect(conversation).to.have.property('type');", "        pm.expect(conversation).to.have.property('participants');", "        pm.expect(conversation).to.have.property('is_encrypted');", "    }", "});"]}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/messaging/conversations/?page=1&page_size=20", "host": ["{{base_url}}"], "path": ["api", "messaging", "conversations", ""], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "20"}]}}}, {"name": "Create Direct Conversation", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201 || pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('conversation_id', response.id);", "    console.log('Conversation ID set:', response.id);", "    pm.test('Conversation created or retrieved', function () {", "        pm.expect(response).to.have.property('id');", "        pm.expect(response).to.have.property('type');", "        pm.expect(response.type).to.equal('DIRECT');", "        pm.expect(response).to.have.property('participants');", "    });", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"type\": \"DIRECT\",\n  \"participant_ids\": [\"{{target_user_id}}\"]\n}"}, "url": {"raw": "{{base_url}}/api/messaging/conversations/create/", "host": ["{{base_url}}"], "path": ["api", "messaging", "conversations", "create", ""]}}}, {"name": "Create Group Conversation", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    pm.test('Group conversation created', function () {", "        pm.expect(response).to.have.property('id');", "        pm.expect(response).to.have.property('type');", "        pm.expect(response.type).to.equal('GROUP');", "        pm.expect(response).to.have.property('name');", "        pm.expect(response).to.have.property('participants');", "    });", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"type\": \"GROUP\",\n  \"name\": \"Test Group Chat\",\n  \"participant_ids\": [\"{{target_user_id}}\"]\n}"}, "url": {"raw": "{{base_url}}/api/messaging/conversations/create/", "host": ["{{base_url}}"], "path": ["api", "messaging", "conversations", "create", ""]}}}, {"name": "Get Conversation Messages", "event": [{"listen": "test", "script": {"exec": ["pm.test('Messages retrieved successfully', function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response).to.have.property('results');", "    pm.expect(response.results).to.be.an('array');", "    if (response.results.length > 0) {", "        const message = response.results[0];", "        pm.expect(message).to.have.property('id');", "        pm.expect(message).to.have.property('sender');", "        pm.expect(message).to.have.property('message_type');", "        pm.expect(message).to.have.property('is_encrypted');", "        pm.expect(message).to.have.property('created_at');", "    }", "});"]}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/messaging/conversations/{{conversation_id}}/messages/?page=1&page_size=50", "host": ["{{base_url}}"], "path": ["api", "messaging", "conversations", "{{conversation_id}}", "messages", ""], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}]}}}, {"name": "Send Plaintext Message", "event": [{"listen": "test", "script": {"exec": ["pm.test('Plaintext message sent successfully', function () {", "    pm.response.to.have.status(201);", "    const response = pm.response.json();", "    pm.expect(response).to.have.property('id');", "    pm.expect(response).to.have.property('content');", "    pm.expect(response.is_encrypted).to.be.false;", "    pm.expect(response.message_type).to.equal('TEXT');", "});"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"content\": \"Hello! This is a plaintext message for testing.\",\n  \"message_type\": \"TEXT\"\n}"}, "url": {"raw": "{{base_url}}/api/messaging/conversations/{{conversation_id}}/send/", "host": ["{{base_url}}"], "path": ["api", "messaging", "conversations", "{{conversation_id}}", "send", ""]}}}, {"name": "Send Encrypted Message", "event": [{"listen": "test", "script": {"exec": ["pm.test('Encrypted message sent successfully', function () {", "    pm.response.to.have.status(201);", "    const response = pm.response.json();", "    pm.expect(response).to.have.property('id');", "    pm.expect(response).to.have.property('encrypted_content');", "    pm.expect(response).to.have.property('iv');", "    pm.expect(response).to.have.property('sender_ratchet_key');", "    pm.expect(response.is_encrypted).to.be.true;", "    pm.expect(response.message_type).to.equal('TEXT');", "});"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"encrypted_content\": \"dGhpcyBpcyBmYWtlIGVuY3J5cHRlZCBjb250ZW50\",\n  \"iv\": \"cmFuZG9tSVY5NmJpdA==\",\n  \"sender_ratchet_key\": \"ZmFrZVJhdGNoZXRLZXlTUEtJRm9ybWF0\",\n  \"message_number\": 1,\n  \"previous_chain_length\": 0,\n  \"message_type\": \"TEXT\"\n}"}, "url": {"raw": "{{base_url}}/api/messaging/conversations/{{conversation_id}}/send/", "host": ["{{base_url}}"], "path": ["api", "messaging", "conversations", "{{conversation_id}}", "send", ""]}}}, {"name": "Get Conversation Encryption Status", "event": [{"listen": "test", "script": {"exec": ["pm.test('Encryption status retrieved successfully', function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response).to.have.property('conversation_id');", "    pm.expect(response).to.have.property('is_encrypted');", "    pm.expect(response).to.have.property('participants');", "    pm.expect(response.participants).to.be.an('array');", "    if (response.participants.length > 0) {", "        const participant = response.participants[0];", "        pm.expect(participant).to.have.property('id');", "        pm.expect(participant).to.have.property('username');", "        pm.expect(participant).to.have.property('has_encryption');", "    }", "});"]}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/messaging/conversations/{{conversation_id}}/encryption-status/", "host": ["{{base_url}}"], "path": ["api", "messaging", "conversations", "{{conversation_id}}", "encryption-status", ""]}}}, {"name": "Search Users", "event": [{"listen": "test", "script": {"exec": ["pm.test('User search completed successfully', function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response).to.have.property('success');", "    pm.expect(response).to.have.property('results');", "    pm.expect(response.results).to.be.an('array');", "    if (response.results.length > 0) {", "        const user = response.results[0];", "        pm.expect(user).to.have.property('id');", "        pm.expect(user).to.have.property('username');", "        pm.expect(user).to.have.property('first_name');", "        pm.expect(user).to.have.property('last_name');", "        pm.expect(user).to.have.property('full_name');", "        // Set target_user_id for other tests", "        pm.collectionVariables.set('target_user_id', user.id);", "    }", "});"]}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/messaging/users/search/?q=test", "host": ["{{base_url}}"], "path": ["api", "messaging", "users", "search", ""], "query": [{"key": "q", "value": "test", "description": "Search query (minimum 2 characters)"}]}}}, {"name": "Get User Profile", "event": [{"listen": "test", "script": {"exec": ["pm.test('User profile retrieved successfully', function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response).to.have.property('success');", "    pm.expect(response).to.have.property('data');", "    const user = response.data;", "    pm.expect(user).to.have.property('id');", "    pm.expect(user).to.have.property('username');", "    pm.expect(user).to.have.property('first_name');", "    pm.expect(user).to.have.property('last_name');", "    pm.expect(user).to.have.property('full_name');", "});"]}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/messaging/users/{{target_user_id}}/", "host": ["{{base_url}}"], "path": ["api", "messaging", "users", "{{target_user_id}}", ""]}}}]}, {"name": "WebSocket Examples", "item": [{"name": "Socket Connection Example", "request": {"method": "GET", "header": [], "url": {"raw": "{{socket_url}}", "host": ["{{socket_url}}"]}, "description": "WebSocket connection example. Use this URL with a WebSocket client.\n\nConnection payload:\n```json\n{\n  \"auth\": {\n    \"token\": \"{{jwt_token}}\"\n  }\n}\n```"}}, {"name": "Send Encrypted Message via Socket", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"event\": \"send_message\",\n  \"data\": {\n    \"conversationId\": \"{{conversation_id}}\",\n    \"tempId\": \"temp-123\",\n    \"encryptedContent\": \"dGhpcyBpcyBmYWtlIGVuY3J5cHRlZCBjb250ZW50\",\n    \"iv\": \"cmFuZG9tSVY5NmJpdA==\",\n    \"senderRatchetKey\": \"ZmFrZVJhdGNoZXRLZXlTUEtJRm9ybWF0\",\n    \"messageNumber\": 1,\n    \"previousChainLength\": 0,\n    \"messageType\": \"TEXT\"\n  }\n}"}, "url": {"raw": "{{base_url}}/websocket-example", "host": ["{{base_url}}"], "path": ["websocket-example"]}, "description": "This is a documentation example. Use a WebSocket client to emit this event:\n\nEvent: `send_message`\nData: See request body"}}, {"name": "Key Exchange Request via Socket", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"event\": \"key_exchange_request\",\n  \"data\": {\n    \"targetUserId\": \"{{target_user_id}}\",\n    \"conversationId\": \"{{conversation_id}}\",\n    \"ephemeralPublicKey\": \"ZmFrZUVwaGVtZXJhbEtleVNQS0k=\"\n  }\n}"}, "url": {"raw": "{{base_url}}/websocket-example", "host": ["{{base_url}}"], "path": ["websocket-example"]}, "description": "This is a documentation example. Use a WebSocket client to emit this event:\n\nEvent: `key_exchange_request`\nData: See request body\n\nExpected response event: `key_exchange_response`"}}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Auto-set authorization header if jwt_token is available", "const token = pm.collectionVariables.get('jwt_token');", "if (token && !pm.request.headers.has('Authorization')) {", "    pm.request.headers.add({", "        key: 'Authorization',", "        value: `Bear<PERSON> ${token}`", "    });", "}"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// Global test to check for common error patterns", "pm.test('No authentication errors', function () {", "    pm.expect(pm.response.code).to.not.equal(401);", "});", "", "pm.test('No server errors', function () {", "    pm.expect(pm.response.code).to.be.below(500);", "});"]}}]}