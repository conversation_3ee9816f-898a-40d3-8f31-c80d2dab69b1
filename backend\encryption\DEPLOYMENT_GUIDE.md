# Phase 3 Encryption Backend Deployment Guide

## Quick Start

### 1. Prerequisites
- Python 3.8+
- PostgreSQL 12+
- Django 4.2+
- Existing Phase 2 messaging system

### 2. Installation Steps

```bash
# 1. Install dependencies
pip install cryptography>=41.0.0

# 2. Add to Django settings
# In settings.py, add 'encryption' to INSTALLED_APPS

# 3. Run migrations
python manage.py makemigrations encryption
python manage.py makemigrations messaging  # For Message model updates
python manage.py migrate

# 4. Add URL configuration
# In urls.py, add: path('api/encryption/', include('encryption.urls'))

# 5. Start server
python manage.py runserver
```

### 3. Verification

```bash
# Test basic connectivity
curl http://127.0.0.1:8000/api/encryption/prekeys/count/
# Should return: {"detail":"Authentication credentials were not provided."}

# Run comprehensive tests
python backend/test_encryption_api.py
```

## Database Schema Changes

### New Tables Created
- `user_key_bundles`: User identity and signed pre-keys
- `one_time_prekeys`: One-time pre-keys for perfect forward secrecy  
- `conversation_sessions`: Encrypted Double Ratchet session state
- `message_keys`: Out-of-order message key storage
- `key_bundle_upload_logs`: Security monitoring and rate limiting

### Updated Tables
- `messages`: Added encryption fields while maintaining backward compatibility
  - `encrypted_content`: AES-GCM encrypted message content (base64)
  - `iv`: 96-bit initialization vector (base64)
  - `sender_ratchet_key`: Sender's DH ratchet public key (SPKI base64)
  - `message_number`: Message counter in Double Ratchet (N)
  - `previous_chain_length`: Previous chain length in Double Ratchet (PN)

### Indexes Created
- Performance indexes on frequently queried fields
- Composite indexes for conversation + message queries
- Security indexes for rate limiting and monitoring

## API Endpoints

### Authentication Required
All endpoints require JWT authentication:
```bash
Authorization: Bearer <jwt_access_token>
```

### Available Endpoints
1. `POST /api/encryption/bundles/` - Upload key bundle
2. `GET /api/encryption/bundles/<user_id>/` - Get key bundle  
3. `POST /api/encryption/prekeys/` - Upload one-time pre-keys
4. `GET /api/encryption/prekeys/count/` - Get pre-key count

### Rate Limits
- Key bundle uploads: 10/hour per user
- One-time pre-key uploads: 50/hour per user

## Security Features Implemented

### ✅ Critical Security Fixes
1. **Atomic One-Time Pre-Key Consumption**
   - Uses `SELECT FOR UPDATE` to prevent race conditions
   - Ensures each OPK is consumed exactly once

2. **Server-Side Signature Verification**
   - Verifies signed pre-key signatures over raw SPKI bytes
   - Uses ECDSA P-256 with SHA-256
   - Rejects invalid signatures immediately

3. **Rate Limiting & Security Monitoring**
   - Per-user and per-IP rate limiting
   - Comprehensive security event logging
   - Failed signature verification tracking

### ✅ Additional Security Measures
- **No Private Keys**: Server never stores or handles private keys
- **Input Validation**: Comprehensive validation with Pydantic schemas
- **Error Handling**: Secure error responses without information leakage
- **Database Transactions**: Atomic operations for consistency
- **CORS Protection**: Proper CORS configuration for production

## Data Flow

### Key Bundle Upload Flow
1. Client generates ECDSA P-256 identity key pair (private key stays on client)
2. Client generates ECDH P-256 signed pre-key pair (private key stays on client)
3. Client signs signed pre-key SPKI bytes with identity private key
4. Client uploads public keys + signature to server
5. Server verifies signature over raw SPKI bytes
6. Server stores public keys only (never private keys)

### Key Exchange Flow
1. Alice requests Bob's key bundle
2. Server returns Bob's public keys + consumes one OPK atomically
3. Alice performs X3DH key exchange locally
4. Alice establishes Double Ratchet session
5. Alice can now send encrypted messages to Bob

### Message Flow
1. Client encrypts message with Double Ratchet message key
2. Client sends encrypted payload to server
3. Server stores encrypted payload (cannot decrypt)
4. Server forwards encrypted payload to recipient
5. Recipient decrypts message locally

## Backward Compatibility

### Phase 2 Integration
- **Existing Messages**: Remain readable as plaintext
- **New Messages**: Automatically encrypted when both users have key bundles
- **API Compatibility**: Existing messaging endpoints unchanged
- **Database Schema**: Additive changes only, no breaking modifications

### Migration Strategy
- **Gradual Rollout**: New conversations automatically use encryption
- **User Choice**: Users can see encryption status
- **Fallback**: Graceful handling when encryption unavailable

## Production Deployment

### Environment Variables
```bash
# Required
SECRET_KEY=your-secret-key
DEBUG=False
ALLOWED_HOSTS=your-domain.com
DATABASE_URL=postgresql://user:pass@host:port/dbname

# Optional
ENCRYPTION_LOG_LEVEL=INFO
RATE_LIMIT_ENABLED=True
```

### Security Hardening
1. **HTTPS Only**: Force HTTPS in production
2. **Database Encryption**: Enable PostgreSQL encryption at rest
3. **Connection Security**: Use SSL for database connections
4. **Log Monitoring**: Set up alerts for security events
5. **Regular Audits**: Schedule periodic security reviews

### Performance Optimization
1. **Database Indexes**: Ensure all indexes are created
2. **Connection Pooling**: Configure database connection pooling
3. **Caching**: Cache key bundle lookups for performance
4. **Monitoring**: Monitor query performance and rate limits

### Monitoring & Alerting
```python
# Key metrics to monitor
- Key bundle upload success rate (should be >95%)
- Signature verification failure rate (should be <1%)
- One-time pre-key consumption rate
- Rate limiting violations
- Database query performance
- API response times
```

## Testing

### Unit Tests
```bash
# Run encryption app tests
python manage.py test encryption

# Run with coverage
coverage run --source='.' manage.py test encryption
coverage report
```

### Integration Tests
```bash
# Run comprehensive API tests
python test_encryption_api.py

# Test specific scenarios
python manage.py shell -c "from encryption.tests import *; run_specific_test()"
```

### Security Tests
- Signature verification with invalid signatures
- Rate limiting enforcement
- Authentication requirement verification
- Database transaction atomicity
- Private key absence verification

## Troubleshooting

### Common Issues
1. **Migration Conflicts**: Use `--fake` for existing fields
2. **Signature Failures**: Verify SPKI byte signing
3. **Rate Limiting**: Check time windows and limits
4. **Authentication**: Verify JWT token format and expiry

### Debug Commands
```bash
# Check database state
python manage.py shell -c "from encryption.models import *; print('Bundles:', UserKeyBundle.objects.count())"

# Verify migrations
python manage.py showmigrations encryption

# Check server logs
tail -f encryption.log
```

### Performance Issues
- Check database indexes: `EXPLAIN ANALYZE` on slow queries
- Monitor connection pool usage
- Review rate limiting configuration
- Optimize key bundle caching

## Next Steps

After successful backend deployment:

1. **Client Implementation**: Implement WebCrypto + libsignal client
2. **End-to-End Testing**: Test full encryption flow
3. **Security Audit**: Professional security review
4. **Performance Testing**: Load testing with realistic data
5. **User Documentation**: Guide for encryption features
6. **Monitoring Setup**: Production monitoring and alerting

## Support

### Documentation
- API Documentation: `backend/encryption/API_DOCUMENTATION.md`
- Implementation Status: `backend/encryption/IMPLEMENTATION_STATUS.md`
- Model Documentation: `backend/encryption/README.md`

### Testing
- Comprehensive test suite: `backend/encryption/tests.py`
- API test script: `backend/test_encryption_api.py`
- Security verification scripts included

### Security
- All critical security fixes implemented
- Server never handles private keys
- Comprehensive input validation
- Rate limiting and monitoring
- Atomic database operations

The Phase 3 encryption backend is production-ready with all security measures implemented!
