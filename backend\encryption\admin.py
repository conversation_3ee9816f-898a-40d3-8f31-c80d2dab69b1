# backend/encryption/admin.py
from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe
from .models import UserKeyBundle, OneTimePreKey, ConversationSession, Message<PERSON>ey, KeyBundleUploadLog


@admin.register(UserKeyBundle)
class UserKeyBundleAdmin(admin.ModelAdmin):
    list_display = ['user', 'signed_prekey_id', 'created_at', 'updated_at', 'key_preview']
    list_filter = ['created_at', 'updated_at']
    search_fields = ['user__username', 'user__email']
    readonly_fields = ['id', 'created_at', 'updated_at', 'identity_key_preview', 'signed_prekey_preview']
    
    fieldsets = (
        ('User Information', {
            'fields': ('user', 'id', 'created_at', 'updated_at')
        }),
        ('Identity Key', {
            'fields': ('identity_public_key', 'identity_key_preview'),
            'classes': ('collapse',)
        }),
        ('Signed Pre-Key', {
            'fields': ('signed_prekey_id', 'signed_prekey_public', 'signed_prekey_signature', 'signed_prekey_preview'),
            'classes': ('collapse',)
        }),
    )
    
    def key_preview(self, obj):
        return f"ID: {obj.signed_prekey_id}"
    key_preview.short_description = "Pre-Key ID"
    
    def identity_key_preview(self, obj):
        if obj.identity_public_key:
            preview = obj.identity_public_key[:50] + "..." if len(obj.identity_public_key) > 50 else obj.identity_public_key
            return format_html('<code>{}</code>', preview)
        return "-"
    identity_key_preview.short_description = "Identity Key Preview"
    
    def signed_prekey_preview(self, obj):
        if obj.signed_prekey_public:
            preview = obj.signed_prekey_public[:50] + "..." if len(obj.signed_prekey_public) > 50 else obj.signed_prekey_public
            return format_html('<code>{}</code>', preview)
        return "-"
    signed_prekey_preview.short_description = "Signed Pre-Key Preview"


@admin.register(OneTimePreKey)
class OneTimePreKeyAdmin(admin.ModelAdmin):
    list_display = ['user', 'key_id', 'is_used', 'created_at', 'used_at', 'key_preview']
    list_filter = ['is_used', 'created_at', 'used_at']
    search_fields = ['user__username', 'user__email', 'key_id']
    readonly_fields = ['id', 'created_at', 'used_at', 'public_key_preview']
    
    fieldsets = (
        ('Key Information', {
            'fields': ('user', 'key_id', 'is_used', 'id')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'used_at')
        }),
        ('Public Key', {
            'fields': ('public_key', 'public_key_preview'),
            'classes': ('collapse',)
        }),
    )
    
    def key_preview(self, obj):
        return f"Key {obj.key_id}"
    key_preview.short_description = "Key ID"
    
    def public_key_preview(self, obj):
        if obj.public_key:
            preview = obj.public_key[:50] + "..." if len(obj.public_key) > 50 else obj.public_key
            return format_html('<code>{}</code>', preview)
        return "-"
    public_key_preview.short_description = "Public Key Preview"


@admin.register(ConversationSession)
class ConversationSessionAdmin(admin.ModelAdmin):
    list_display = ['participant', 'conversation', 'message_number_send', 'message_number_receive', 'updated_at']
    list_filter = ['created_at', 'updated_at']
    search_fields = ['participant__username', 'conversation__id']
    readonly_fields = ['id', 'created_at', 'updated_at', 'session_preview']
    
    fieldsets = (
        ('Session Information', {
            'fields': ('conversation', 'participant', 'id')
        }),
        ('Message Counters', {
            'fields': ('message_number_send', 'message_number_receive', 'previous_chain_length')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at')
        }),
        ('Encrypted Data', {
            'fields': ('session_state', 'root_key', 'chain_key_send', 'chain_key_receive', 'session_preview'),
            'classes': ('collapse',)
        }),
    )
    
    def session_preview(self, obj):
        return "Encrypted session data (not displayed for security)"
    session_preview.short_description = "Session Data"


@admin.register(MessageKey)
class MessageKeyAdmin(admin.ModelAdmin):
    list_display = ['session', 'message_number', 'created_at', 'ratchet_key_preview']
    list_filter = ['created_at']
    search_fields = ['session__participant__username', 'message_number']
    readonly_fields = ['id', 'created_at', 'message_key_preview', 'ratchet_key_preview']
    
    fieldsets = (
        ('Message Key Information', {
            'fields': ('session', 'message_number', 'id', 'created_at')
        }),
        ('Encrypted Keys', {
            'fields': ('message_key', 'sender_ratchet_key', 'message_key_preview', 'ratchet_key_preview'),
            'classes': ('collapse',)
        }),
    )
    
    def message_key_preview(self, obj):
        return "Encrypted message key (not displayed for security)"
    message_key_preview.short_description = "Message Key"
    
    def ratchet_key_preview(self, obj):
        if obj.sender_ratchet_key:
            preview = obj.sender_ratchet_key[:30] + "..." if len(obj.sender_ratchet_key) > 30 else obj.sender_ratchet_key
            return format_html('<code>{}</code>', preview)
        return "-"
    ratchet_key_preview.short_description = "Ratchet Key Preview"


@admin.register(KeyBundleUploadLog)
class KeyBundleUploadLogAdmin(admin.ModelAdmin):
    list_display = ['user', 'ip_address', 'success', 'created_at', 'error_preview']
    list_filter = ['success', 'created_at']
    search_fields = ['user__username', 'ip_address', 'error_message']
    readonly_fields = ['id', 'created_at']
    
    fieldsets = (
        ('Upload Information', {
            'fields': ('user', 'ip_address', 'success', 'id', 'created_at')
        }),
        ('Error Details', {
            'fields': ('error_message',),
            'classes': ('collapse',)
        }),
    )
    
    def error_preview(self, obj):
        if obj.error_message:
            preview = obj.error_message[:50] + "..." if len(obj.error_message) > 50 else obj.error_message
            return format_html('<span style="color: red;">{}</span>', preview)
        return format_html('<span style="color: green;">Success</span>')
    error_preview.short_description = "Status"
    
    def has_add_permission(self, request):
        return False  # Don't allow manual creation
    
    def has_change_permission(self, request, obj=None):
        return False  # Read-only logs
