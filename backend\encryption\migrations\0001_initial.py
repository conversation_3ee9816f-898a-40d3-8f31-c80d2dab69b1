# Generated by Django 5.2.4 on 2025-08-27 05:13

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('messaging', '0003_message_encrypted_content_message_iv_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='ConversationSession',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('session_state', models.JSONField(help_text='Encrypted Double Ratchet session state')),
                ('root_key', models.TextField(help_text='Root key for Double Ratchet (encrypted, base64)')),
                ('chain_key_send', models.TextField(blank=True, help_text='Send chain key (encrypted, base64)', null=True)),
                ('chain_key_receive', models.TextField(blank=True, help_text='Receive chain key (encrypted, base64)', null=True)),
                ('message_number_send', models.IntegerField(default=0, help_text='Send message counter (Ns)')),
                ('message_number_receive', models.IntegerField(default=0, help_text='Receive message counter (Nr)')),
                ('previous_chain_length', models.IntegerField(default=0, help_text='Previous chain length (PNs)')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('conversation', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='encryption_sessions', to='messaging.conversation')),
                ('participant', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='conversation_sessions', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Conversation Session',
                'verbose_name_plural': 'Conversation Sessions',
                'db_table': 'conversation_sessions',
            },
        ),
        migrations.CreateModel(
            name='KeyBundleUploadLog',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('ip_address', models.GenericIPAddressField()),
                ('success', models.BooleanField(default=True)),
                ('error_message', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='bundle_uploads', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Key Bundle Upload Log',
                'verbose_name_plural': 'Key Bundle Upload Logs',
                'db_table': 'key_bundle_upload_logs',
            },
        ),
        migrations.CreateModel(
            name='MessageKey',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('message_number', models.IntegerField(help_text='Message number this key is for')),
                ('message_key', models.TextField(help_text='Message key (encrypted, base64 encoded)')),
                ('sender_ratchet_key', models.TextField(help_text="Sender's ratchet public key (SPKI format, base64)")),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('session', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='message_keys', to='encryption.conversationsession')),
            ],
            options={
                'verbose_name': 'Message Key',
                'verbose_name_plural': 'Message Keys',
                'db_table': 'message_keys',
            },
        ),
        migrations.CreateModel(
            name='OneTimePreKey',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('key_id', models.IntegerField(help_text='One-time pre-key identifier (unique per user)')),
                ('public_key', models.TextField(help_text='ECDH P-256 public key (SPKI format, base64 encoded)')),
                ('is_used', models.BooleanField(default=False, help_text='Whether this one-time pre-key has been consumed')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('used_at', models.DateTimeField(blank=True, help_text='When this key was consumed', null=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='one_time_prekeys', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'One-Time Pre-Key',
                'verbose_name_plural': 'One-Time Pre-Keys',
                'db_table': 'one_time_prekeys',
            },
        ),
        migrations.CreateModel(
            name='UserKeyBundle',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('identity_public_key', models.TextField(help_text='ECDSA P-256 public key (SPKI format, base64 encoded)')),
                ('signed_prekey_id', models.IntegerField(help_text='Signed pre-key identifier')),
                ('signed_prekey_public', models.TextField(help_text='ECDH P-256 public key (SPKI format, base64 encoded)')),
                ('signed_prekey_signature', models.TextField(help_text='ECDSA signature over signed pre-key SPKI bytes (base64 encoded)')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='key_bundle', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'User Key Bundle',
                'verbose_name_plural': 'User Key Bundles',
                'db_table': 'user_key_bundles',
            },
        ),
        migrations.AddIndex(
            model_name='conversationsession',
            index=models.Index(fields=['conversation', 'participant'], name='conversatio_convers_170dde_idx'),
        ),
        migrations.AddIndex(
            model_name='conversationsession',
            index=models.Index(fields=['updated_at'], name='conversatio_updated_2cfa54_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='conversationsession',
            unique_together={('conversation', 'participant')},
        ),
        migrations.AddIndex(
            model_name='keybundleuploadlog',
            index=models.Index(fields=['user', 'created_at'], name='key_bundle__user_id_de82a1_idx'),
        ),
        migrations.AddIndex(
            model_name='keybundleuploadlog',
            index=models.Index(fields=['ip_address', 'created_at'], name='key_bundle__ip_addr_d36791_idx'),
        ),
        migrations.AddIndex(
            model_name='keybundleuploadlog',
            index=models.Index(fields=['success', 'created_at'], name='key_bundle__success_b70a97_idx'),
        ),
        migrations.AddIndex(
            model_name='messagekey',
            index=models.Index(fields=['session', 'message_number'], name='message_key_session_78960f_idx'),
        ),
        migrations.AddIndex(
            model_name='messagekey',
            index=models.Index(fields=['created_at'], name='message_key_created_fd0ccb_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='messagekey',
            unique_together={('session', 'sender_ratchet_key', 'message_number')},
        ),
        migrations.AddIndex(
            model_name='onetimeprekey',
            index=models.Index(fields=['user', 'is_used'], name='one_time_pr_user_id_565628_idx'),
        ),
        migrations.AddIndex(
            model_name='onetimeprekey',
            index=models.Index(fields=['created_at'], name='one_time_pr_created_1f3399_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='onetimeprekey',
            unique_together={('user', 'key_id')},
        ),
    ]
