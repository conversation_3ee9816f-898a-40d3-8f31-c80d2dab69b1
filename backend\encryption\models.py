# backend/encryption/models.py
import uuid
from django.db import models
from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError
from django.utils import timezone

User = get_user_model()


class UserKeyBundle(models.Model):
    """
    Stores user's public key bundle for key exchange.
    Contains identity key (ECDSA P-256) and signed pre-key (ECDH P-256).
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.OneToOneField(
        User, 
        on_delete=models.CASCADE, 
        related_name='key_bundle'
    )
    
    # Identity key (ECDSA P-256 public key in SPKI format, base64 encoded)
    identity_public_key = models.TextField(
        help_text="ECDSA P-256 public key (SPKI format, base64 encoded)"
    )
    
    # Signed pre-key (ECDH P-256)
    signed_prekey_id = models.IntegerField(
        help_text="Signed pre-key identifier"
    )
    signed_prekey_public = models.TextField(
        help_text="ECDH P-256 public key (SPKI format, base64 encoded)"
    )
    signed_prekey_signature = models.TextField(
        help_text="ECDSA signature over signed pre-key SPKI bytes (base64 encoded)"
    )
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'user_key_bundles'
        verbose_name = 'User Key Bundle'
        verbose_name_plural = 'User Key Bundles'
    
    def __str__(self):
        return f"Key bundle for {self.user.username}"
    
    def clean(self):
        """Validate key bundle data"""
        if not self.identity_public_key:
            raise ValidationError("Identity public key is required")
        if not self.signed_prekey_public:
            raise ValidationError("Signed pre-key public key is required")
        if not self.signed_prekey_signature:
            raise ValidationError("Signed pre-key signature is required")


class OneTimePreKey(models.Model):
    """
    One-time pre-keys for perfect forward secrecy.
    Each key can only be used once for session establishment.
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(
        User, 
        on_delete=models.CASCADE, 
        related_name='one_time_prekeys'
    )
    
    key_id = models.IntegerField(
        help_text="One-time pre-key identifier (unique per user)"
    )
    public_key = models.TextField(
        help_text="ECDH P-256 public key (SPKI format, base64 encoded)"
    )
    
    is_used = models.BooleanField(
        default=False,
        help_text="Whether this one-time pre-key has been consumed"
    )
    
    created_at = models.DateTimeField(auto_now_add=True)
    used_at = models.DateTimeField(
        null=True, 
        blank=True,
        help_text="When this key was consumed"
    )
    
    class Meta:
        db_table = 'one_time_prekeys'
        unique_together = ['user', 'key_id']
        verbose_name = 'One-Time Pre-Key'
        verbose_name_plural = 'One-Time Pre-Keys'
        indexes = [
            models.Index(fields=['user', 'is_used']),
            models.Index(fields=['created_at']),
        ]
    
    def __str__(self):
        status = "used" if self.is_used else "available"
        return f"OPK {self.key_id} for {self.user.username} ({status})"
    
    def mark_as_used(self):
        """Mark this one-time pre-key as used"""
        self.is_used = True
        self.used_at = timezone.now()
        self.save(update_fields=['is_used', 'used_at'])


class ConversationSession(models.Model):
    """
    Stores Double Ratchet session state for each participant in a conversation.
    Contains encrypted session state and ratchet keys.
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    conversation = models.ForeignKey(
        'messaging.Conversation', 
        on_delete=models.CASCADE, 
        related_name='encryption_sessions'
    )
    participant = models.ForeignKey(
        User, 
        on_delete=models.CASCADE, 
        related_name='conversation_sessions'
    )
    
    # Encrypted session state (JSON, encrypted with user's master key)
    session_state = models.JSONField(
        help_text="Encrypted Double Ratchet session state"
    )
    
    # Root key (base64 encoded, encrypted with user's key)
    root_key = models.TextField(
        help_text="Root key for Double Ratchet (encrypted, base64)"
    )
    
    # Chain keys (base64 encoded, encrypted)
    chain_key_send = models.TextField(
        null=True, 
        blank=True,
        help_text="Send chain key (encrypted, base64)"
    )
    chain_key_receive = models.TextField(
        null=True, 
        blank=True,
        help_text="Receive chain key (encrypted, base64)"
    )
    
    # Message counters
    message_number_send = models.IntegerField(
        default=0,
        help_text="Send message counter (Ns)"
    )
    message_number_receive = models.IntegerField(
        default=0,
        help_text="Receive message counter (Nr)"
    )
    previous_chain_length = models.IntegerField(
        default=0,
        help_text="Previous chain length (PNs)"
    )
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'conversation_sessions'
        unique_together = ['conversation', 'participant']
        verbose_name = 'Conversation Session'
        verbose_name_plural = 'Conversation Sessions'
        indexes = [
            models.Index(fields=['conversation', 'participant']),
            models.Index(fields=['updated_at']),
        ]
    
    def __str__(self):
        return f"Session for {self.participant.username} in conversation {self.conversation.id}"


class MessageKey(models.Model):
    """
    Stores message keys for handling out-of-order and skipped messages.
    Part of Double Ratchet's skipped message key store.
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    session = models.ForeignKey(
        ConversationSession, 
        on_delete=models.CASCADE, 
        related_name='message_keys'
    )
    
    message_number = models.IntegerField(
        help_text="Message number this key is for"
    )
    message_key = models.TextField(
        help_text="Message key (encrypted, base64 encoded)"
    )
    sender_ratchet_key = models.TextField(
        help_text="Sender's ratchet public key (SPKI format, base64)"
    )
    
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        db_table = 'message_keys'
        unique_together = ['session', 'sender_ratchet_key', 'message_number']
        verbose_name = 'Message Key'
        verbose_name_plural = 'Message Keys'
        indexes = [
            models.Index(fields=['session', 'message_number']),
            models.Index(fields=['created_at']),
        ]
    
    def __str__(self):
        return f"Message key {self.message_number} for session {self.session.id}"


# Rate limiting model for security
class KeyBundleUploadLog(models.Model):
    """
    Tracks key bundle uploads for rate limiting and security monitoring.
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='bundle_uploads')
    ip_address = models.GenericIPAddressField()
    success = models.BooleanField(default=True)
    error_message = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        db_table = 'key_bundle_upload_logs'
        verbose_name = 'Key Bundle Upload Log'
        verbose_name_plural = 'Key Bundle Upload Logs'
        indexes = [
            models.Index(fields=['user', 'created_at']),
            models.Index(fields=['ip_address', 'created_at']),
            models.Index(fields=['success', 'created_at']),
        ]
