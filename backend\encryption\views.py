# backend/encryption/views.py
"""
Encryption API endpoints with security hardening.
SECURITY: All endpoints include rate limiting, signature verification, and atomic operations.
"""
from rest_framework import status, permissions
from rest_framework.decorators import api_view, permission_classes, throttle_classes
from rest_framework.response import Response
from rest_framework.throttling import UserRateThrottle, AnonRateThrottle
from django.shortcuts import get_object_or_404
from django.db import transaction
from django.utils import timezone
from django.contrib.auth import get_user_model
from pydantic_core import ValidationError
import logging

from .models import UserKeyBundle, OneTimePreKey, KeyBundleUploadLog
from .schemas import (
    KeyBundleUpload, KeyBundleResponse, OneTimePreKeyBatch,
    EncryptedMessageCreate, ErrorResponse
)
from .crypto_utils import (
    verify_signed_prekey_signature, validate_ecdh_public_key,
    get_client_ip, is_rate_limited, CryptoError, SignatureVerificationError
)

User = get_user_model()
logger = logging.getLogger(__name__)


class KeyBundleUploadThrottle(UserRateThrottle):
    """Custom throttle for key bundle uploads"""
    scope = 'key_bundle_upload'
    rate = '10/hour'  # Allow 10 uploads per hour per user


class OneTimePreKeyThrottle(UserRateThrottle):
    """Custom throttle for one-time pre-key uploads"""
    scope = 'one_time_prekey_upload'
    rate = '50/hour'  # Allow 50 batches per hour per user


def log_key_bundle_operation(user, ip_address: str, success: bool, error_message: str = ""):
    """Log key bundle operations for security monitoring"""
    try:
        KeyBundleUploadLog.objects.create(
            user=user,
            ip_address=ip_address,
            success=success,
            error_message=error_message
        )
    except Exception as e:
        logger.error(f"Failed to log key bundle operation: {e}")


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
@throttle_classes([KeyBundleUploadThrottle])
def upload_key_bundle(request):
    """
    Upload user's public key bundle with signature verification.
    
    SECURITY FEATURES:
    - Rate limiting per user and IP
    - Signature verification over raw SPKI bytes
    - Atomic database operations
    - Security event logging
    """
    ip_address = get_client_ip(request)
    
    # Additional rate limiting check
    if is_rate_limited(request.user, ip_address):
        log_key_bundle_operation(request.user, ip_address, False, "Rate limited")
        return Response(
            {'error': 'Rate limit exceeded. Please try again later.'},
            status=status.HTTP_429_TOO_MANY_REQUESTS
        )
    
    try:
        # Validate input with Pydantic
        bundle_data = KeyBundleUpload(**request.data)
        
        # SECURITY CRITICAL: Verify signed pre-key signature
        signature_valid = verify_signed_prekey_signature(
            bundle_data.identity_public_key,
            bundle_data.signed_prekey_public,
            bundle_data.signed_prekey_signature
        )
        
        if not signature_valid:
            log_key_bundle_operation(
                request.user, ip_address, False, 
                "Invalid signed pre-key signature"
            )
            return Response(
                {
                    'error': 'Invalid signed pre-key signature',
                    'code': 'SIGNATURE_INVALID'
                },
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Validate ECDH public key format
        if not validate_ecdh_public_key(bundle_data.signed_prekey_public):
            log_key_bundle_operation(
                request.user, ip_address, False,
                "Invalid ECDH public key format"
            )
            return Response(
                {
                    'error': 'Invalid signed pre-key format',
                    'code': 'KEY_FORMAT_INVALID'
                },
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Atomic database operation
        with transaction.atomic():
            # Delete existing key bundle
            UserKeyBundle.objects.filter(user=request.user).delete()
            
            # Create new key bundle
            key_bundle = UserKeyBundle.objects.create(
                user=request.user,
                identity_public_key=bundle_data.identity_public_key,
                signed_prekey_id=bundle_data.signed_prekey_id,
                signed_prekey_public=bundle_data.signed_prekey_public,
                signed_prekey_signature=bundle_data.signed_prekey_signature
            )
        
        log_key_bundle_operation(request.user, ip_address, True)
        
        logger.info(f"Key bundle uploaded successfully for user {request.user.username}")
        
        return Response({
            'key_bundle_id': str(key_bundle.id),
            'message': 'Key bundle uploaded successfully'
        }, status=status.HTTP_201_CREATED)
        
    except ValidationError as e:
        log_key_bundle_operation(request.user, ip_address, False, f"Validation error: {e}")
        return Response(
            {'error': 'Invalid input data', 'details': e.errors()},
            status=status.HTTP_400_BAD_REQUEST
        )
    except (CryptoError, SignatureVerificationError) as e:
        log_key_bundle_operation(request.user, ip_address, False, str(e))
        return Response(
            {'error': str(e), 'code': 'CRYPTO_ERROR'},
            status=status.HTTP_400_BAD_REQUEST
        )
    except Exception as e:
        log_key_bundle_operation(request.user, ip_address, False, f"Unexpected error: {e}")
        logger.error(f"Unexpected error in upload_key_bundle: {e}")
        return Response(
            {'error': 'Internal server error'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def get_key_bundle(request, user_id):
    """
    Get another user's key bundle for key exchange.
    
    SECURITY FEATURES:
    - Atomic one-time pre-key consumption
    - Rate limiting
    - Security event logging
    """
    ip_address = get_client_ip(request)
    
    try:
        # Get target user
        target_user = get_object_or_404(User, id=user_id)
        
        # Get key bundle
        try:
            key_bundle = UserKeyBundle.objects.get(user=target_user)
        except UserKeyBundle.DoesNotExist:
            return Response(
                {'error': 'Key bundle not found for user', 'code': 'KEY_BUNDLE_NOT_FOUND'},
                status=status.HTTP_404_NOT_FOUND
            )
        
        # SECURITY CRITICAL: Atomic one-time pre-key consumption
        one_time_prekey = None
        with transaction.atomic():
            opk_query = OneTimePreKey.objects.select_for_update().filter(
                user=target_user,
                is_used=False
            )
            
            one_time_prekey = opk_query.first()
            if one_time_prekey:
                one_time_prekey.is_used = True
                one_time_prekey.used_at = timezone.now()
                one_time_prekey.save(update_fields=['is_used', 'used_at'])
        
        # Build response
        response_data = {
            'identity_public_key': key_bundle.identity_public_key,
            'signed_prekey': {
                'id': key_bundle.signed_prekey_id,
                'public_key': key_bundle.signed_prekey_public,
                'signature': key_bundle.signed_prekey_signature
            }
        }
        
        if one_time_prekey:
            response_data['one_time_prekey'] = {
                'id': one_time_prekey.key_id,
                'public_key': one_time_prekey.public_key
            }
        
        logger.info(
            f"Key bundle retrieved for user {target_user.username} "
            f"by {request.user.username}, OPK consumed: {bool(one_time_prekey)}"
        )
        
        return Response(response_data)
        
    except Exception as e:
        logger.error(f"Error in get_key_bundle: {e}")
        return Response(
            {'error': 'Internal server error'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
@throttle_classes([OneTimePreKeyThrottle])
def upload_one_time_prekeys(request):
    """
    Upload batch of one-time pre-keys.
    
    SECURITY FEATURES:
    - Rate limiting
    - Key format validation
    - Atomic batch operations
    - Duplicate prevention
    """
    ip_address = get_client_ip(request)
    
    try:
        # Validate input
        batch_data = OneTimePreKeyBatch(**request.data)
        
        # Validate all keys before creating any
        for prekey_data in batch_data.prekeys:
            if not validate_ecdh_public_key(prekey_data.public_key):
                return Response(
                    {
                        'error': f'Invalid ECDH public key format for key ID {prekey_data.key_id}',
                        'code': 'KEY_FORMAT_INVALID'
                    },
                    status=status.HTTP_400_BAD_REQUEST
                )
        
        # Atomic batch creation
        created_prekeys = []
        with transaction.atomic():
            for prekey_data in batch_data.prekeys:
                # Check for existing key ID
                if OneTimePreKey.objects.filter(
                    user=request.user, 
                    key_id=prekey_data.key_id
                ).exists():
                    return Response(
                        {
                            'error': f'Key ID {prekey_data.key_id} already exists',
                            'code': 'DUPLICATE_KEY_ID'
                        },
                        status=status.HTTP_400_BAD_REQUEST
                    )
                
                prekey = OneTimePreKey.objects.create(
                    user=request.user,
                    key_id=prekey_data.key_id,
                    public_key=prekey_data.public_key
                )
                created_prekeys.append(prekey)
        
        logger.info(
            f"Uploaded {len(created_prekeys)} one-time pre-keys for user {request.user.username}"
        )
        
        return Response({
            'message': f'{len(created_prekeys)} one-time pre-keys uploaded successfully',
            'count': len(created_prekeys)
        }, status=status.HTTP_201_CREATED)
        
    except ValidationError as e:
        return Response(
            {'error': 'Invalid input data', 'details': e.errors()},
            status=status.HTTP_400_BAD_REQUEST
        )
    except Exception as e:
        logger.error(f"Error in upload_one_time_prekeys: {e}")
        return Response(
            {'error': 'Internal server error'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def get_prekey_count(request):
    """Get count of available one-time pre-keys for the authenticated user"""
    try:
        available_count = OneTimePreKey.objects.filter(
            user=request.user,
            is_used=False
        ).count()
        
        total_count = OneTimePreKey.objects.filter(
            user=request.user
        ).count()
        
        return Response({
            'available_count': available_count,
            'total_count': total_count,
            'used_count': total_count - available_count
        })
        
    except Exception as e:
        logger.error(f"Error in get_prekey_count: {e}")
        return Response(
            {'error': 'Internal server error'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )
