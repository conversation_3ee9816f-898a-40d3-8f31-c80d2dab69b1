# backend/messaging/models.py
import uuid
from django.db import models
from django.contrib.auth import get_user_model

User = get_user_model()

class Conversation(models.Model):
    CONVERSATION_TYPES = [
        ('DIRECT', 'Direct Message'),
        ('GROUP', 'Group Chat'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    type = models.CharField(max_length=10, choices=CONVERSATION_TYPES, default='DIRECT')
    name = models.CharField(max_length=100, blank=True, null=True)  # For group chats
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'conversations'
        ordering = ['-updated_at']

    def __str__(self):
        if self.type == 'GROUP':
            return self.name or f"Group {self.id}"
        return f"Direct conversation {self.id}"

class ConversationParticipant(models.Model):
    PARTICIPANT_ROLES = [
        ('ADMIN', 'Admin'),
        ('MEMBER', 'Member'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    conversation = models.ForeignKey(Conversation, on_delete=models.CASCADE, related_name='participants')
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='conversation_memberships')
    role = models.CharField(max_length=10, choices=PARTICIPANT_ROLES, default='MEMBER')
    joined_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'conversation_participants'
        unique_together = ['conversation', 'user']

    def __str__(self):
        return f"{self.user.username} in {self.conversation}"

class Message(models.Model):
    MESSAGE_TYPES = [
        ('TEXT', 'Text Message'),
        ('IMAGE', 'Image'),
        ('FILE', 'File'),
        ('SYSTEM', 'System Message'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    conversation = models.ForeignKey(Conversation, on_delete=models.CASCADE, related_name='messages')
    sender = models.ForeignKey(User, on_delete=models.CASCADE, related_name='sent_messages')

    # PHASE 3: Backward compatibility - keep for existing messages
    content = models.TextField(
        blank=True,
        help_text="Plaintext content (Phase 2 compatibility, empty for encrypted messages)"
    )

    message_type = models.CharField(max_length=10, choices=MESSAGE_TYPES, default='TEXT')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    # PHASE 3: Encryption fields
    encrypted_content = models.TextField(
        blank=True,
        help_text="AES-GCM encrypted message content (base64 encoded)"
    )
    iv = models.TextField(
        blank=True,
        help_text="96-bit initialization vector (base64 encoded)"
    )
    sender_ratchet_key = models.TextField(
        blank=True,
        help_text="Sender's current DH ratchet public key (SPKI format, base64)"
    )
    message_number = models.IntegerField(
        default=0,
        help_text="Message counter in Double Ratchet (N)"
    )
    previous_chain_length = models.IntegerField(
        default=0,
        help_text="Previous chain length in Double Ratchet (PN)"
    )

    class Meta:
        db_table = 'messages'
        ordering = ['created_at']
        indexes = [
            models.Index(fields=['conversation', 'created_at']),
            models.Index(fields=['sender', 'created_at']),
            models.Index(fields=['conversation', 'message_number']),
        ]

    def __str__(self):
        return f"Message from {self.sender.username} in {self.conversation}"

    @property
    def is_encrypted(self):
        """Check if this message is encrypted"""
        return bool(self.encrypted_content)

    def get_content(self):
        """Get message content (encrypted or plaintext for backward compatibility)"""
        if self.encrypted_content:
            return "[Encrypted Message]"  # Client will decrypt
        return self.content


class MessageStatus(models.Model):
    STATUS_TYPES = [
        ('SENT', 'Sent'),
        ('DELIVERED', 'Delivered'),
        ('READ', 'Read'),
        ('FAILED', 'Failed'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    message = models.ForeignKey(Message, on_delete=models.CASCADE, related_name='statuses')
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='message_statuses')
    status = models.CharField(max_length=10, choices=STATUS_TYPES, default='SENT')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'message_statuses'
        unique_together = ['message', 'user']
        ordering = ['created_at']

    def __str__(self):
        return f"{self.user.username} - {self.message.id} - {self.status}"
