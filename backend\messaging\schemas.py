# backend/messaging/schemas.py
from pydantic import BaseModel, Field, field_validator, model_validator, ConfigDict
from typing import Optional, List
from datetime import datetime
from enum import Enum
import uuid
import base64

class ConversationType(str, Enum):
    DIRECT = "DIRECT"
    GROUP = "GROUP"

class MessageType(str, Enum):
    TEXT = "TEXT"
    IMAGE = "IMAGE"
    FILE = "FILE"
    SYSTEM = "SYSTEM"

class MessageStatusType(str, Enum):
    SENT = "SENT"
    DELIVERED = "DELIVERED"
    READ = "READ"
    FAILED = "FAILED"

class ParticipantRole(str, Enum):
    ADMIN = "ADMIN"
    MEMBER = "MEMBER"

class UserBasic(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: uuid.UUID
    username: str
    first_name: str
    last_name: str
    profile_picture: Optional[str] = None

class MessageCreate(BaseModel):
    # Plaintext message fields (Phase 2 compatibility)
    content: Optional[str] = Field(None, max_length=4000)
    message_type: MessageType = MessageType.TEXT
    reply_to_id: Optional[uuid.UUID] = None

    # Phase 3: Encryption fields (optional for backward compatibility)
    encrypted_content: Optional[str] = Field(None, description="AES-GCM encrypted message content (base64)")
    iv: Optional[str] = Field(None, description="96-bit initialization vector (base64)")
    sender_ratchet_key: Optional[str] = Field(None, description="Sender's DH ratchet public key (SPKI base64)")
    message_number: Optional[int] = Field(None, ge=0, description="Message counter in Double Ratchet")
    previous_chain_length: Optional[int] = Field(None, ge=0, description="Previous chain length in Double Ratchet")

    @model_validator(mode='after')
    def validate_message_content(self):
        """Ensure either plaintext content or encrypted content is provided"""
        has_plaintext = bool(self.content and self.content.strip())
        has_encrypted = bool(self.encrypted_content)

        if not has_plaintext and not has_encrypted:
            raise ValueError('Either content or encrypted_content must be provided')

        if has_encrypted:
            # Validate required encryption fields
            if not all([self.encrypted_content, self.iv, self.sender_ratchet_key,
                       self.message_number is not None, self.previous_chain_length is not None]):
                raise ValueError('All encryption fields are required for encrypted messages')

            # Validate base64 encoding
            try:
                base64.b64decode(self.encrypted_content)
                base64.b64decode(self.iv)
                base64.b64decode(self.sender_ratchet_key)
            except Exception:
                raise ValueError('Invalid base64 encoding in encryption fields')

            # Validate IV length (96 bits = 12 bytes)
            try:
                iv_bytes = base64.b64decode(self.iv)
                if len(iv_bytes) != 12:
                    raise ValueError('IV must be exactly 96 bits (12 bytes)')
            except Exception:
                raise ValueError('Invalid IV format')

        return self

class MessageResponse(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: uuid.UUID
    conversation_id: uuid.UUID
    sender: UserBasic
    message_type: MessageType
    created_at: datetime
    updated_at: datetime
    status: Optional[MessageStatusType] = None

    # Content fields (conditional based on encryption status)
    content: Optional[str] = None  # For plaintext messages
    is_encrypted: bool = False  # Indicates if message is encrypted

    # Phase 3: Encryption fields (only present for encrypted messages)
    encrypted_content: Optional[str] = None
    iv: Optional[str] = None
    sender_ratchet_key: Optional[str] = None
    message_number: Optional[int] = None
    previous_chain_length: Optional[int] = None

class MessageStatusResponse(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: uuid.UUID
    message_id: uuid.UUID
    user_id: uuid.UUID
    status: MessageStatusType
    created_at: datetime
    updated_at: datetime

class MessageStatusUpdate(BaseModel):
    message_id: uuid.UUID
    status: MessageStatusType

class ConversationCreate(BaseModel):
    type: ConversationType
    name: Optional[str] = Field(None, max_length=100)
    participant_ids: List[uuid.UUID] = Field(..., min_items=1)

    @model_validator(mode='after')
    def validate_name_for_group(self):
        if self.type == ConversationType.GROUP and not self.name:
            raise ValueError('Group conversations must have a name')
        return self

class UserWithEncryption(UserBasic):
    """User info with encryption status"""
    has_encryption: bool = False

class ConversationResponse(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: uuid.UUID
    type: ConversationType
    name: Optional[str] = None
    participants: List[UserBasic]
    last_message: Optional[MessageResponse] = None
    created_at: datetime
    updated_at: datetime

    # Phase 3: Encryption status
    is_encrypted: bool = False  # Whether all participants have encryption enabled
    encryption_participants: Optional[List[UserWithEncryption]] = None  # Detailed encryption status
