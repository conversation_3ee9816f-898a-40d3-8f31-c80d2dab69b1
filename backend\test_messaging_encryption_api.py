#!/usr/bin/env python3
"""
Test script for Phase 3 messaging API encryption integration.
Tests the updated messaging endpoints to ensure they properly handle encrypted messages.
"""

import requests
import json
import sys
import os

# Add the backend directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Django setup
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'chatapp.settings')
import django
django.setup()

from django.contrib.auth import get_user_model
from messaging.models import Conversation, ConversationParticipant, Message
from encryption.models import UserKeyBundle, OneTimePreKey

User = get_user_model()

# Test configuration
BASE_URL = 'http://127.0.0.1:8000'
TEST_USERS = [
    {
        'username': 'alice_msg_test',
        'email': '<EMAIL>',
        'password': 'testpass123',
        'first_name': 'Alice',
        'last_name': 'MessageTest',
    },
    {
        'username': 'bob_msg_test',
        'email': '<EMAIL>',
        'password': 'testpass123',
        'first_name': 'Bob',
        'last_name': 'MessageTest',
    }
]

# Mock encryption data
MOCK_KEY_BUNDLE = {
    'identity_public_key': 'MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEVkpOnGmFntib9F/I6tIQmWeEDO186HPnDEKbTtMfiqAslpRFuBc6vvenIetWqyYJu/hXt25NdcJFoVAk2yr6zw==',
    'signed_prekey_id': 1,
    'signed_prekey_public': 'MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAE9o1LVOhahkUcLCAZgtF/2YpK8V7brrv0j5Au+B95HJ4lyDWAPMZfoEYJFYyE+7PaFG+lxo2B/jBch0aeTlI3dQ==',
    'signed_prekey_signature': 'MEUCIF4z5ltcLiOHEBq8ykORetLXOh4oD3yZjGtCKu8Rv/QSAiEAjK8Y4pOJYSGOPEX65j2XLK9IgkMjC7zRXMtUAvrUxEQ='
}

MOCK_ENCRYPTED_MESSAGE = {
    'encrypted_content': 'dGhpcyBpcyBmYWtlIGVuY3J5cHRlZCBjb250ZW50',  # base64 fake encrypted content
    'iv': 'MTIzNDU2Nzg5MDEy',  # base64 encoded 12-byte IV (exactly 96 bits)
    'sender_ratchet_key': 'ZmFrZVJhdGNoZXRLZXlTUEtJRm9ybWF0',  # base64 fake SPKI key
    'message_number': 1,
    'previous_chain_length': 0,
    'message_type': 'TEXT'
}

def setup_test_data():
    """Create test users and set up encryption for one user"""
    print("🔧 Setting up test data...")
    
    # Create test users
    users = []
    for user_data in TEST_USERS:
        user, created = User.objects.get_or_create(
            username=user_data['username'],
            defaults={
                'email': user_data['email'],
                'first_name': user_data['first_name'],
                'last_name': user_data['last_name'],
            }
        )
        if created:
            user.set_password(user_data['password'])
            user.save()
        users.append(user)
    
    # Set up encryption for Alice (first user)
    alice = users[0]
    key_bundle, created = UserKeyBundle.objects.get_or_create(
        user=alice,
        defaults={
            'identity_public_key': MOCK_KEY_BUNDLE['identity_public_key'],
            'signed_prekey_id': MOCK_KEY_BUNDLE['signed_prekey_id'],
            'signed_prekey_public': MOCK_KEY_BUNDLE['signed_prekey_public'],
            'signed_prekey_signature': MOCK_KEY_BUNDLE['signed_prekey_signature'],
        }
    )
    
    # Add some one-time pre-keys for Alice
    for i in range(1, 6):
        OneTimePreKey.objects.get_or_create(
            user=alice,
            key_id=i,
            defaults={
                'public_key': f'ZmFrZU9US0tleXtpfQ==',  # fake base64 key
            }
        )
    
    print(f"✅ Test users created: {[u.username for u in users]}")
    print(f"✅ Encryption enabled for: {alice.username}")
    return users

def authenticate_user(username, password):
    """Authenticate user and return JWT token"""
    # Find the user data to get the correct email
    user_data = next((u for u in TEST_USERS if u['username'] == username), None)
    if not user_data:
        print(f"❌ User data not found for {username}")
        return None, None

    response = requests.post(f'{BASE_URL}/api/auth/login/', json={
        'email': user_data['email'],
        'password': password
    })
    
    if response.status_code == 200:
        data = response.json()
        if 'data' in data and 'tokens' in data['data']:
            # New authentication response format
            return data['data']['tokens']['access'], data['data']['user']['id']
        elif 'access' in data:
            # Old authentication response format
            return data['access'], data['user']['id']
        else:
            print(f"❌ Unexpected auth response format: {data}")
            return None, None
    else:
        print(f"❌ Authentication failed for {username}: {response.status_code} - {response.text}")
        return None, None

def test_create_conversation(token, participant_id):
    """Test conversation creation"""
    print("\n📝 Testing conversation creation...")
    
    headers = {'Authorization': f'Bearer {token}'}
    response = requests.post(f'{BASE_URL}/api/messaging/conversations/create/', 
                           headers=headers,
                           json={
                               'type': 'DIRECT',
                               'name': 'Test Encrypted Conversation',
                               'participant_ids': [participant_id]
                           })
    
    if response.status_code in [200, 201]:
        conversation = response.json()
        print(f"✅ Conversation created: {conversation['id']}")
        print(f"   Encryption status: {conversation.get('is_encrypted', 'Not provided')}")
        return conversation['id']
    else:
        print(f"❌ Conversation creation failed: {response.status_code} - {response.text}")
        return None

def test_send_plaintext_message(token, conversation_id):
    """Test sending plaintext message"""
    print("\n📤 Testing plaintext message sending...")
    
    headers = {'Authorization': f'Bearer {token}'}
    response = requests.post(f'{BASE_URL}/api/messaging/conversations/{conversation_id}/send/',
                           headers=headers,
                           json={
                               'content': 'This is a plaintext message for backward compatibility',
                               'message_type': 'TEXT'
                           })
    
    if response.status_code == 201:
        message = response.json()
        print(f"✅ Plaintext message sent: {message['id']}")
        print(f"   Content: {message.get('content', 'Not provided')}")
        print(f"   Is encrypted: {message.get('is_encrypted', 'Not provided')}")
        return message['id']
    else:
        print(f"❌ Plaintext message failed: {response.status_code} - {response.text}")
        return None

def test_send_encrypted_message(token, conversation_id):
    """Test sending encrypted message"""
    print("\n🔐 Testing encrypted message sending...")
    
    headers = {'Authorization': f'Bearer {token}'}
    message_data = MOCK_ENCRYPTED_MESSAGE.copy()
    
    response = requests.post(f'{BASE_URL}/api/messaging/conversations/{conversation_id}/send/',
                           headers=headers,
                           json=message_data)
    
    if response.status_code == 201:
        message = response.json()
        print(f"✅ Encrypted message sent: {message['id']}")
        print(f"   Is encrypted: {message.get('is_encrypted', 'Not provided')}")
        print(f"   Encrypted content: {message.get('encrypted_content', 'Not provided')[:20]}...")
        print(f"   IV: {message.get('iv', 'Not provided')}")
        print(f"   Message number: {message.get('message_number', 'Not provided')}")
        return message['id']
    else:
        print(f"❌ Encrypted message failed: {response.status_code} - {response.text}")
        return None

def test_get_conversation_messages(token, conversation_id):
    """Test retrieving conversation messages"""
    print("\n📋 Testing message retrieval...")
    
    headers = {'Authorization': f'Bearer {token}'}
    response = requests.get(f'{BASE_URL}/api/messaging/conversations/{conversation_id}/messages/',
                          headers=headers)
    
    if response.status_code == 200:
        data = response.json()
        messages = data.get('results', [])
        print(f"✅ Retrieved {len(messages)} messages")
        
        for i, message in enumerate(messages):
            is_encrypted = message.get('is_encrypted', False)
            print(f"   Message {i+1}: {'Encrypted' if is_encrypted else 'Plaintext'}")
            if is_encrypted:
                print(f"     Encrypted content: {message.get('encrypted_content', 'N/A')[:20]}...")
                print(f"     Message number: {message.get('message_number', 'N/A')}")
            else:
                print(f"     Content: {message.get('content', 'N/A')[:50]}...")
        
        return messages
    else:
        print(f"❌ Message retrieval failed: {response.status_code} - {response.text}")
        return []

def test_conversation_encryption_status(token, conversation_id):
    """Test conversation encryption status endpoint"""
    print("\n🔍 Testing conversation encryption status...")
    
    headers = {'Authorization': f'Bearer {token}'}
    response = requests.get(f'{BASE_URL}/api/messaging/conversations/{conversation_id}/encryption-status/',
                          headers=headers)
    
    if response.status_code == 200:
        status_data = response.json()
        print(f"✅ Encryption status retrieved")
        print(f"   Conversation encrypted: {status_data.get('is_encrypted', 'N/A')}")
        print(f"   Participants:")
        for participant in status_data.get('participants', []):
            print(f"     - {participant['username']}: {'✅' if participant['has_encryption'] else '❌'} encryption")
        return status_data
    else:
        print(f"❌ Encryption status failed: {response.status_code} - {response.text}")
        return None

def test_conversation_list_with_encryption(token):
    """Test conversation list with encryption status"""
    print("\n📜 Testing conversation list with encryption status...")
    
    headers = {'Authorization': f'Bearer {token}'}
    response = requests.get(f'{BASE_URL}/api/messaging/conversations/', headers=headers)
    
    if response.status_code == 200:
        data = response.json()
        conversations = data.get('results', [])
        print(f"✅ Retrieved {len(conversations)} conversations")
        
        for conv in conversations:
            print(f"   Conversation: {conv['name'] or 'Direct Message'}")
            print(f"     Encrypted: {conv.get('is_encrypted', 'N/A')}")
            if 'encryption_participants' in conv:
                for p in conv['encryption_participants']:
                    print(f"     - {p['username']}: {'✅' if p['has_encryption'] else '❌'}")
        
        return conversations
    else:
        print(f"❌ Conversation list failed: {response.status_code} - {response.text}")
        return []

def cleanup_test_data():
    """Clean up test data"""
    print("\n🧹 Cleaning up test data...")
    
    # Delete test users and related data
    for user_data in TEST_USERS:
        try:
            user = User.objects.get(username=user_data['username'])
            user.delete()
            print(f"✅ Deleted user: {user_data['username']}")
        except User.DoesNotExist:
            pass

def main():
    """Run all messaging API encryption tests"""
    print("🚀 Starting Phase 3 Messaging API Encryption Tests")
    print("=" * 60)
    
    try:
        # Setup
        users = setup_test_data()
        alice_username = users[0].username
        bob_username = users[1].username
        
        # Authenticate users
        alice_token, alice_id = authenticate_user(alice_username, 'testpass123')
        bob_token, bob_id = authenticate_user(bob_username, 'testpass123')
        
        if not alice_token or not bob_token:
            print("❌ Authentication failed, aborting tests")
            return
        
        print(f"✅ Authenticated: {alice_username} and {bob_username}")
        
        # Test conversation creation
        conversation_id = test_create_conversation(alice_token, bob_id)
        if not conversation_id:
            print("❌ Conversation creation failed, aborting tests")
            return
        
        # Test message sending (both types)
        plaintext_msg_id = test_send_plaintext_message(alice_token, conversation_id)
        encrypted_msg_id = test_send_encrypted_message(alice_token, conversation_id)
        
        # Test message retrieval
        messages = test_get_conversation_messages(bob_token, conversation_id)
        
        # Test encryption status
        encryption_status = test_conversation_encryption_status(alice_token, conversation_id)
        
        # Test conversation list with encryption
        conversations = test_conversation_list_with_encryption(alice_token)
        
        # Summary
        print("\n" + "=" * 60)
        print("🎉 Test Summary:")
        print(f"✅ Conversation created: {bool(conversation_id)}")
        print(f"✅ Plaintext message sent: {bool(plaintext_msg_id)}")
        print(f"✅ Encrypted message sent: {bool(encrypted_msg_id)}")
        print(f"✅ Messages retrieved: {len(messages)} messages")
        print(f"✅ Encryption status checked: {bool(encryption_status)}")
        print(f"✅ Conversation list retrieved: {len(conversations)} conversations")
        
        if all([conversation_id, plaintext_msg_id, encrypted_msg_id, messages, encryption_status]):
            print("\n🎉 All messaging API encryption tests passed!")
        else:
            print("\n⚠️  Some tests failed - check output above")
        
    except Exception as e:
        print(f"❌ Test execution failed: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        cleanup_test_data()

if __name__ == '__main__':
    main()
