# backend/users/schemas.py
from pydantic import BaseModel, EmailStr, Field, ConfigDict
from typing import Optional
from datetime import datetime
import uuid

class UserBase(BaseModel):
    email: EmailStr
    username: str = Field(..., min_length=3, max_length=30)
    first_name: str = Field(..., min_length=1, max_length=30)
    last_name: str = Field(..., min_length=1, max_length=30)

class UserCreate(UserBase):
    password: str = Field(..., min_length=8)

class UserLogin(BaseModel):
    email: EmailStr
    password: str

class UserResponse(UserBase):
    model_config = ConfigDict(from_attributes=True)

    id: uuid.UUID
    profile_picture: Optional[str] = None
    is_verified: bool
    last_seen: datetime
    created_at: datetime

class TokenResponse(BaseModel):
    access: str
    refresh: str

class AuthResponse(BaseModel):
    user: UserResponse
    tokens: TokenResponse
