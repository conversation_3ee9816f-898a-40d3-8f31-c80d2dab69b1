import { test, expect } from '@playwright/test';

test.describe('E2E Framework Validation', () => {
  test('should validate Playwright setup', async ({ page }) => {
    // Test basic Playwright functionality
    await page.goto('https://playwright.dev');
    await expect(page).toHaveTitle(/Playwright/);
    
    // Test page interactions
    const getStartedButton = page.locator('text=Get started');
    if (await getStartedButton.isVisible()) {
      await expect(getStartedButton).toBeVisible();
    }
  });

  test('should validate browser capabilities', async ({ page }) => {
    await page.setContent('<h1>Test Page</h1><p>Framework validation test</p>');
    
    await expect(page.locator('h1')).toHaveText('Test Page');
    await expect(page.locator('p')).toHaveText('Framework validation test');
  });

  test('should validate JavaScript execution', async ({ page }) => {
    await page.setContent(`
      <div id="test">Initial</div>
      <script>
        setTimeout(() => {
          document.getElementById('test').textContent = 'Updated';
        }, 100);
      </script>
    `);
    
    // Wait for JavaScript to execute
    await expect(page.locator('#test')).toHaveText('Updated');
  });

  test('should validate form interactions', async ({ page }) => {
    await page.setContent(`
      <form>
        <input type="email" id="email" placeholder="Email">
        <input type="password" id="password" placeholder="Password">
        <button type="submit" id="submit">Submit</button>
      </form>
    `);
    
    // Test form filling
    await page.fill('#email', '<EMAIL>');
    await page.fill('#password', 'password123');
    
    // Verify values
    await expect(page.locator('#email')).toHaveValue('<EMAIL>');
    await expect(page.locator('#password')).toHaveValue('password123');
    
    // Test button interaction
    await expect(page.locator('#submit')).toBeEnabled();
  });

  test('should validate CSS and styling', async ({ page }) => {
    await page.setContent(`
      <style>
        .test-element {
          color: red;
          display: block;
          width: 200px;
          height: 100px;
        }
      </style>
      <div class="test-element">Styled Element</div>
    `);
    
    const element = page.locator('.test-element');
    await expect(element).toBeVisible();
    
    // Check computed styles
    const color = await element.evaluate(el => getComputedStyle(el).color);
    expect(color).toBe('rgb(255, 0, 0)'); // red
  });

  test('should validate responsive design simulation', async ({ page }) => {
    await page.setContent('<div>Responsive Test</div>');
    
    // Test different viewport sizes
    const viewports = [
      { width: 1920, height: 1080 }, // Desktop
      { width: 768, height: 1024 },  // Tablet
      { width: 375, height: 667 }    // Mobile
    ];
    
    for (const viewport of viewports) {
      await page.setViewportSize(viewport);
      
      const actualViewport = page.viewportSize();
      expect(actualViewport?.width).toBe(viewport.width);
      expect(actualViewport?.height).toBe(viewport.height);
      
      // Verify content is still visible
      await expect(page.locator('div')).toBeVisible();
    }
  });

  test('should validate local storage operations', async ({ page }) => {
    await page.goto('data:text/html,<div>Storage Test</div>');
    
    // Test localStorage
    await page.evaluate(() => {
      localStorage.setItem('test-key', 'test-value');
      sessionStorage.setItem('session-key', 'session-value');
    });
    
    const localValue = await page.evaluate(() => localStorage.getItem('test-key'));
    const sessionValue = await page.evaluate(() => sessionStorage.getItem('session-key'));
    
    expect(localValue).toBe('test-value');
    expect(sessionValue).toBe('session-value');
  });

  test('should validate error handling', async ({ page }) => {
    const consoleErrors: string[] = [];
    
    page.on('console', msg => {
      if (msg.type() === 'error') {
        consoleErrors.push(msg.text());
      }
    });
    
    await page.setContent(`
      <div>Error Test</div>
      <script>
        // This should generate a console error
        try {
          nonExistentFunction();
        } catch (e) {
          console.error('Expected test error:', e.message);
        }
      </script>
    `);
    
    await page.waitForTimeout(1000);
    
    // Should have captured the console error
    expect(consoleErrors.length).toBeGreaterThan(0);
    expect(consoleErrors[0]).toContain('Expected test error');
  });

  test('should validate network request interception', async ({ page }) => {
    // Intercept and mock a network request
    await page.route('**/api/test', route => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ message: 'Mocked response' })
      });
    });
    
    await page.setContent(`
      <div id="result">Loading...</div>
      <script>
        fetch('/api/test')
          .then(response => response.json())
          .then(data => {
            document.getElementById('result').textContent = data.message;
          })
          .catch(error => {
            document.getElementById('result').textContent = 'Error: ' + error.message;
          });
      </script>
    `);
    
    // Wait for the mocked response
    await expect(page.locator('#result')).toHaveText('Mocked response');
  });

  test('should validate multi-context scenarios', async ({ browser }) => {
    // Create multiple browser contexts
    const context1 = await browser.newContext();
    const context2 = await browser.newContext();
    
    try {
      const page1 = await context1.newPage();
      const page2 = await context2.newPage();
      
      // Set different content in each context
      await page1.setContent('<div>Context 1</div>');
      await page2.setContent('<div>Context 2</div>');
      
      // Verify isolation
      await expect(page1.locator('div')).toHaveText('Context 1');
      await expect(page2.locator('div')).toHaveText('Context 2');
      
      // Test localStorage isolation
      await page1.evaluate(() => localStorage.setItem('context', 'first'));
      await page2.evaluate(() => localStorage.setItem('context', 'second'));
      
      const value1 = await page1.evaluate(() => localStorage.getItem('context'));
      const value2 = await page2.evaluate(() => localStorage.getItem('context'));
      
      expect(value1).toBe('first');
      expect(value2).toBe('second');
      
    } finally {
      await context1.close();
      await context2.close();
    }
  });

  test('should validate performance measurement', async ({ page }) => {
    const startTime = Date.now();
    
    await page.goto('https://playwright.dev');
    
    const loadTime = Date.now() - startTime;
    
    // Page should load within reasonable time (10 seconds)
    expect(loadTime).toBeLessThan(10000);
    
    // Test JavaScript performance
    const jsPerformance = await page.evaluate(() => {
      const start = performance.now();
      
      // Perform some operations
      const arr = Array.from({ length: 1000 }, (_, i) => i);
      const doubled = arr.map(x => x * 2);
      const sum = doubled.reduce((a, b) => a + b, 0);
      
      const end = performance.now();
      return {
        duration: end - start,
        result: sum
      };
    });
    
    expect(jsPerformance.duration).toBeLessThan(100);
    expect(jsPerformance.result).toBe(999000);
  });
});
