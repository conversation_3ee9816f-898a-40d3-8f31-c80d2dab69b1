import { test, expect } from '@playwright/test';
import { DashboardPage } from '../../page-objects/DashboardPage';
import { TestDataManager } from '../../utils/test-data-manager';

test.describe('Debug Conversation Creation', () => {
  let dashboardPage: DashboardPage;
  let testDataManager: TestDataManager;

  test.beforeEach(async ({ page }) => {
    dashboardPage = new DashboardPage(page);
    testDataManager = new TestDataManager();
    
    // Login as test user 1
    const testUser = testDataManager.getTestUser('primary');
    await page.goto('/login');
    await page.fill('[data-testid="email-input"]', testUser.email);
    await page.fill('[data-testid="password-input"]', testUser.password);
    await page.click('[data-testid="login-button"]');
    
    // Wait for dashboard to load
    await dashboardPage.waitForPageLoad();
  });

  test('should debug conversation creation step by step', async ({ page }) => {
    console.log('Starting conversation creation debug...');
    
    // Check if dashboard loaded properly
    await expect(dashboardPage.header).toBeVisible();
    console.log('✓ Dashboard header is visible');
    
    await expect(dashboardPage.conversationList).toBeVisible();
    console.log('✓ Conversation list is visible');
    
    await expect(dashboardPage.connectionStatus).toBeVisible();
    console.log('✓ Connection status is visible');
    
    // Check connection status
    const connectionText = await dashboardPage.connectionStatus.textContent();
    console.log('Connection status:', connectionText);
    
    // Check if new chat button is visible
    await expect(dashboardPage.newChatButton).toBeVisible();
    console.log('✓ New chat button is visible');
    
    // Click new chat button
    await dashboardPage.newChatButton.click();
    console.log('✓ Clicked new chat button');
    
    // Wait for user search modal
    await expect(dashboardPage.userSearchModal).toBeVisible({ timeout: 10000 });
    console.log('✓ User search modal is visible');
    
    // Check if search input is visible
    await expect(dashboardPage.userSearchInput).toBeVisible();
    console.log('✓ User search input is visible');
    
    // Search for test user 2
    const testUser2 = testDataManager.getTestUser('secondary');
    await dashboardPage.userSearchInput.fill(testUser2.username);
    console.log('✓ Filled search input with:', testUser2.username);
    
    // Wait for search results
    await page.waitForTimeout(2000);
    
    // Check if search results appear
    const searchResults = page.locator('[data-testid="user-result"]');
    const resultCount = await searchResults.count();
    console.log('Search results count:', resultCount);
    
    if (resultCount > 0) {
      console.log('✓ Search results found');

      // Click on the Chat button in the first result
      const firstResult = searchResults.first();
      const chatButton = firstResult.locator('[data-testid="user-action-button"]');
      await expect(chatButton).toBeVisible();
      await chatButton.click();
      console.log('✓ Clicked on Chat button in first search result');
      
      // Wait for modal to close
      await expect(dashboardPage.userSearchModal).toBeHidden({ timeout: 10000 });
      console.log('✓ User search modal closed');
      
      // Check if chat area becomes visible
      await page.waitForTimeout(2000);
      
      const isChatAreaVisible = await dashboardPage.chatArea.isVisible();
      console.log('Chat area visible:', isChatAreaVisible);
      
      if (isChatAreaVisible) {
        console.log('✓ Chat area is visible');
        
        // Check if message input is visible
        const isMessageInputVisible = await dashboardPage.messageInput.isVisible();
        console.log('Message input visible:', isMessageInputVisible);
        
        if (isMessageInputVisible) {
          console.log('✓ Message input is visible');
          
          // Check if send button is visible
          const isSendButtonVisible = await dashboardPage.sendButton.isVisible();
          console.log('Send button visible:', isSendButtonVisible);
          
          if (isSendButtonVisible) {
            console.log('✓ Send button is visible');
            console.log('🎉 Conversation creation successful!');
          } else {
            console.log('❌ Send button not visible');
          }
        } else {
          console.log('❌ Message input not visible');
          
          // Let's check what's in the chat area
          const chatAreaContent = await dashboardPage.chatArea.textContent();
          console.log('Chat area content:', chatAreaContent);
          
          // Check if there are any error messages
          const errorElements = page.locator('.error, .alert-error, [data-testid="error"]');
          const errorCount = await errorElements.count();
          console.log('Error elements count:', errorCount);
          
          if (errorCount > 0) {
            for (let i = 0; i < errorCount; i++) {
              const errorText = await errorElements.nth(i).textContent();
              console.log('Error', i + 1, ':', errorText);
            }
          }
        }
      } else {
        console.log('❌ Chat area not visible');
        
        // Check what's currently visible on the page
        const pageContent = await page.textContent('body');
        console.log('Page content preview:', pageContent?.substring(0, 500));
      }
    } else {
      console.log('❌ No search results found');
      
      // Check if there are any error messages in the search
      const searchAreaContent = await dashboardPage.userSearchResults.textContent();
      console.log('Search area content:', searchAreaContent);
    }
  });

  test('should check if conversation list has existing conversations', async ({ page }) => {
    console.log('Checking existing conversations...');
    
    const conversationCount = await dashboardPage.getConversationCount();
    console.log('Existing conversation count:', conversationCount);
    
    if (conversationCount > 0) {
      console.log('✓ Found existing conversations');
      
      // Try to select the first conversation
      await dashboardPage.selectConversation(0);
      console.log('✓ Selected first conversation');
      
      // Check if chat area loads
      const isChatAreaVisible = await dashboardPage.chatArea.isVisible();
      console.log('Chat area visible after selecting conversation:', isChatAreaVisible);
      
      if (isChatAreaVisible) {
        const isMessageInputVisible = await dashboardPage.messageInput.isVisible();
        console.log('Message input visible in existing conversation:', isMessageInputVisible);
      }
    } else {
      console.log('No existing conversations found');
    }
  });

  test('should check component loading states', async ({ page }) => {
    console.log('Checking component loading states...');
    
    // Check if any loading spinners are present
    const loadingSpinners = page.locator('.loading, .spinner, [data-testid="loading"]');
    const spinnerCount = await loadingSpinners.count();
    console.log('Loading spinner count:', spinnerCount);
    
    if (spinnerCount > 0) {
      for (let i = 0; i < spinnerCount; i++) {
        const spinnerText = await loadingSpinners.nth(i).textContent();
        console.log('Loading spinner', i + 1, ':', spinnerText);
      }
    }
    
    // Check console errors
    const consoleLogs: string[] = [];
    page.on('console', msg => {
      if (msg.type() === 'error') {
        consoleLogs.push(`${msg.type()}: ${msg.text()}`);
      }
    });
    
    // Wait a bit to capture any console errors
    await page.waitForTimeout(3000);
    
    if (consoleLogs.length > 0) {
      console.log('Console errors found:');
      consoleLogs.forEach((log, index) => {
        console.log(`Error ${index + 1}:`, log);
      });
    } else {
      console.log('✓ No console errors found');
    }
  });

  test('should debug message sending', async ({ page }) => {
    console.log('Starting message sending debug...');

    // Listen to console logs to track Redux actions and conversation ID changes
    const consoleLogs: string[] = [];
    page.on('console', msg => {
      const text = msg.text();
      if (text.includes('conversation') || text.includes('message') || text.includes('draft') || text.includes('optimistic')) {
        consoleLogs.push(`${msg.type()}: ${text}`);
        console.log(`Browser [${msg.type()}]:`, text);
      }
    });

    // Create conversation first
    const testUser2 = testDataManager.getTestUser('secondary');
    await dashboardPage.createDirectConversation(testUser2.username);
    console.log('✓ Conversation created');

    // Wait a moment for any async operations
    await page.waitForTimeout(1000);

    // Check initial message count
    const initialCount = await dashboardPage.getMessageCount();
    console.log('Initial message count:', initialCount);

    // Check if message input is enabled
    const isInputEnabled = await dashboardPage.messageInput.isEnabled();
    console.log('Message input enabled:', isInputEnabled);

    // Check if send button is enabled (should be disabled when input is empty)
    const isSendButtonEnabled = await dashboardPage.sendButton.isEnabled();
    console.log('Send button enabled (empty input):', isSendButtonEnabled);

    // Type a message
    const messageText = 'Debug test message';
    await dashboardPage.messageInput.fill(messageText);
    console.log('✓ Filled message input with:', messageText);

    // Check if send button becomes enabled
    const isSendButtonEnabledAfterTyping = await dashboardPage.sendButton.isEnabled();
    console.log('Send button enabled (after typing):', isSendButtonEnabledAfterTyping);

    // Check message input value
    const inputValue = await dashboardPage.messageInput.inputValue();
    console.log('Message input value:', inputValue);

    // Click send button
    await dashboardPage.sendButton.click();
    console.log('✓ Clicked send button');

    // Wait a moment for any immediate updates and log any console messages
    await page.waitForTimeout(3000);

    console.log('\n--- Console logs during message sending ---');
    consoleLogs.forEach((log, index) => {
      console.log(`${index + 1}: ${log}`);
    });
    console.log('--- End console logs ---\n');

    // Check if input was cleared
    const inputValueAfterSend = await dashboardPage.messageInput.inputValue();
    console.log('Message input value after send:', inputValueAfterSend);

    // Check message count after sending
    const countAfterSend = await dashboardPage.getMessageCount();
    console.log('Message count after send:', countAfterSend);

    if (countAfterSend > initialCount) {
      console.log('✓ Message appeared in UI');

      // Get the last message content
      const lastMessage = await dashboardPage.getLastMessage();
      console.log('Last message content:', lastMessage);

      // Check if message has loading state
      const messageElement = page.locator('[data-testid="message"]').last();
      const hasLoadingText = await messageElement.locator('text=Sending...').isVisible();
      console.log('Message has loading state:', hasLoadingText);

      // Wait for loading to complete
      if (hasLoadingText) {
        await expect(messageElement.locator('text=Sending...')).toBeHidden({ timeout: 10000 });
        console.log('✓ Loading state completed');
      }

      // Check for status indicator
      const statusIndicator = messageElement.locator('[data-testid="message-status"], .message-status');
      const hasStatusIndicator = await statusIndicator.isVisible();
      console.log('Message has status indicator:', hasStatusIndicator);

    } else {
      console.log('❌ Message did not appear in UI');

      // Check for any error messages
      const errorElements = page.locator('.error, .alert-error, [data-testid="error"]');
      const errorCount = await errorElements.count();
      console.log('Error elements count:', errorCount);

      if (errorCount > 0) {
        for (let i = 0; i < errorCount; i++) {
          const errorText = await errorElements.nth(i).textContent();
          console.log('Error', i + 1, ':', errorText);
        }
      }

      // Check console errors
      const consoleLogs: string[] = [];
      page.on('console', msg => {
        if (msg.type() === 'error') {
          consoleLogs.push(`${msg.type()}: ${msg.text()}`);
        }
      });

      await page.waitForTimeout(2000);

      if (consoleLogs.length > 0) {
        console.log('Console errors:');
        consoleLogs.forEach((log, index) => {
          console.log(`Error ${index + 1}:`, log);
        });
      }
    }
  });
});
