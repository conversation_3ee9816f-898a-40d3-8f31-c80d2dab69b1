import { test, expect } from '@playwright/test';
import { DashboardPage } from '../../page-objects/DashboardPage';
import { TestDataManager } from '../../utils/test-data-manager';

test.describe('Message Status Indicators Tests', () => {
  let testDataManager: TestDataManager;

  test.beforeEach(async () => {
    testDataManager = new TestDataManager();
  });

  test('should show visual status indicators for message progression: sending → sent → delivered', async ({ browser }) => {
    // Create two browser contexts for two users
    const context1 = await browser.newContext();
    const context2 = await browser.newContext();
    
    const page1 = await context1.newPage();
    const page2 = await context2.newPage();
    
    const dashboard1 = new DashboardPage(page1);
    const dashboard2 = new DashboardPage(page2);
    
    // Login both users
    const testUser1 = testDataManager.getTestUser('primary');
    const testUser2 = testDataManager.getTestUser('secondary');
    
    await page1.goto('/login');
    await page1.fill('[data-testid="email-input"]', testUser1.email);
    await page1.fill('[data-testid="password-input"]', testUser1.password);
    await page1.click('[data-testid="login-button"]');
    await dashboard1.waitForPageLoad();
    
    await page2.goto('/login');
    await page2.fill('[data-testid="email-input"]', testUser2.email);
    await page2.fill('[data-testid="password-input"]', testUser2.password);
    await page2.click('[data-testid="login-button"]');
    await dashboard2.waitForPageLoad();
    
    // User 1 creates conversation with User 2
    await dashboard1.createDirectConversation(testUser2.username);
    
    const messageText = 'Status indicator test message';
    
    // Send message from User 1
    await dashboard1.sendMessage(messageText);
    
    // Get the message element for User 1 (sender)
    const messageElement1 = page1.locator('[data-testid="message"]').last();
    
    // 1. VERIFY SENDING STATE
    // Check for loading indicator during sending (may be very brief)
    const loadingIndicator = messageElement1.locator('text=Sending..., .animate-spin');
    const hasLoadingState = await Promise.race([
      loadingIndicator.isVisible().then(() => true),
      page1.waitForTimeout(500).then(() => false)
    ]);
    
    if (hasLoadingState) {
      console.log('✓ Loading state visible during sending');
      // Wait for loading to complete
      await expect(loadingIndicator).toBeHidden({ timeout: 10000 });
    } else {
      console.log('ℹ Loading state too fast to capture (acceptable)');
    }
    
    // 2. VERIFY SENT STATE
    // Look for status indicator (should appear after sending)
    const statusIndicator1 = messageElement1.locator('[data-testid="message-status"], .message-status, svg[class*="check"], [role="img"]');
    await expect(statusIndicator1).toBeVisible({ timeout: 5000 });
    console.log('✓ Status indicator visible for sender');
    
    // Check for single check mark (SENT status)
    const singleCheck = messageElement1.locator('svg[class*="check"]:not([class*="check-check"])');
    const hasSingleCheck = await singleCheck.isVisible();
    if (hasSingleCheck) {
      console.log('✓ Single check mark visible (SENT status)');
    }
    
    // 3. VERIFY DELIVERED STATE
    // User 2 should receive the message, which should trigger DELIVERED status
    await page2.waitForTimeout(2000);
    
    // Check if User 2 sees the conversation
    const conversationCount2 = await dashboard2.getConversationCount();
    if (conversationCount2 > 0) {
      await dashboard2.selectConversation(0);
      
      // Wait for message to appear for User 2
      await expect(async () => {
        const messageCount2 = await dashboard2.getMessageCount();
        expect(messageCount2).toBeGreaterThan(0);
      }).toPass({ timeout: 10000 });
      
      console.log('✓ Message delivered to User 2');
      
      // Check if status indicator on User 1's side updates to DELIVERED
      await page1.waitForTimeout(2000);
      
      // Look for double check mark or different color indicating DELIVERED
      const deliveredIndicator = messageElement1.locator('svg[class*="check-check"], svg[class*="gray-500"], [class*="delivered"]');
      const hasDeliveredIndicator = await deliveredIndicator.isVisible();
      if (hasDeliveredIndicator) {
        console.log('✓ Delivered status indicator visible');
      } else {
        console.log('⚠ Delivered status indicator not visible - may need implementation');
      }
    }
    
    // 4. VERIFY READ STATE
    // User 2 views the message (scroll into view to trigger read receipt)
    if (conversationCount2 > 0) {
      const messageElement2 = page2.locator('[data-testid="message"]').last();
      await messageElement2.scrollIntoViewIfNeeded();
      await page2.waitForTimeout(1000);
      
      // Check if status indicator on User 1's side updates to READ (green double check)
      await page1.waitForTimeout(2000);
      
      const readIndicator = messageElement1.locator('svg[class*="green"], [class*="read"], svg[class*="check-check"][class*="green"]');
      const hasReadIndicator = await readIndicator.isVisible();
      if (hasReadIndicator) {
        console.log('✓ Read status indicator visible (green)');
      } else {
        console.log('⚠ Read status indicator not visible - may need implementation');
      }
    }
    
    await context1.close();
    await context2.close();
  });

  test('should show different status indicators for different message states', async ({ page }) => {
    const dashboard = new DashboardPage(page);
    
    // Login
    const testUser1 = testDataManager.getTestUser('primary');
    const testUser2 = testDataManager.getTestUser('secondary');
    
    await page.goto('/login');
    await page.fill('[data-testid="email-input"]', testUser1.email);
    await page.fill('[data-testid="password-input"]', testUser1.password);
    await page.click('[data-testid="login-button"]');
    await dashboard.waitForPageLoad();
    
    // Create conversation
    await dashboard.createDirectConversation(testUser2.username);
    
    // Send multiple messages to test different states
    const messages = [
      'First message',
      'Second message', 
      'Third message'
    ];
    
    for (const messageText of messages) {
      await dashboard.sendMessage(messageText);
      await page.waitForTimeout(1000);
    }
    
    // Wait for all messages to be processed
    await page.waitForTimeout(3000);
    
    // Check that all messages have status indicators
    const messageElements = page.locator('[data-testid="message"]');
    const messageCount = await messageElements.count();
    
    console.log(`Checking status indicators for ${messageCount} messages`);
    
    for (let i = 0; i < messageCount; i++) {
      const messageElement = messageElements.nth(i);
      const messageText = await messageElement.textContent();
      
      // Check if this message has a status indicator
      const statusIndicator = messageElement.locator('[data-testid="message-status"], .message-status, svg, [role="img"]');
      const hasStatusIndicator = await statusIndicator.isVisible();
      
      console.log(`Message ${i + 1} ("${messageText?.substring(0, 20)}..."): Status indicator visible = ${hasStatusIndicator}`);
      
      if (hasStatusIndicator) {
        // Check the color/style of the status indicator
        const indicatorElement = statusIndicator.first();
        const classes = await indicatorElement.getAttribute('class') || '';
        console.log(`  Status indicator classes: ${classes}`);
        
        // Check for specific status types
        const isGray = classes.includes('gray');
        const isGreen = classes.includes('green');
        const isRed = classes.includes('red');
        
        if (isGreen) {
          console.log('  ✓ Green indicator (READ status)');
        } else if (isGray) {
          console.log('  ✓ Gray indicator (SENT/DELIVERED status)');
        } else if (isRed) {
          console.log('  ✓ Red indicator (FAILED status)');
        } else {
          console.log('  ℹ Other status indicator style');
        }
      }
    }
  });

  test('should show retry button for failed messages', async ({ page }) => {
    const dashboard = new DashboardPage(page);
    
    // Login
    const testUser1 = testDataManager.getTestUser('primary');
    const testUser2 = testDataManager.getTestUser('secondary');
    
    await page.goto('/login');
    await page.fill('[data-testid="email-input"]', testUser1.email);
    await page.fill('[data-testid="password-input"]', testUser1.password);
    await page.click('[data-testid="login-button"]');
    await dashboard.waitForPageLoad();
    
    // Create conversation
    await dashboard.createDirectConversation(testUser2.username);
    
    // Simulate network disconnection to cause message failure
    await page.route('**/socket.io/**', route => route.abort());
    
    const messageText = 'This message should fail';
    
    // Try to send a message while disconnected
    await dashboard.sendMessage(messageText);
    
    // Wait for failure to be detected
    await page.waitForTimeout(5000);
    
    // Look for retry button or failed status indicator
    const messageElement = page.locator('[data-testid="message"]').last();
    
    // Check for retry button
    const retryButton = messageElement.locator('button:has-text("Retry"), [data-testid="retry-button"]');
    const hasRetryButton = await retryButton.isVisible();
    
    if (hasRetryButton) {
      console.log('✓ Retry button visible for failed message');
      
      // Restore network connection
      await page.unroute('**/socket.io/**');
      
      // Click retry button
      await retryButton.click();
      console.log('✓ Clicked retry button');
      
      // Wait for retry to complete
      await page.waitForTimeout(3000);
      
      // Verify retry button disappears
      await expect(retryButton).toBeHidden({ timeout: 5000 });
      console.log('✓ Retry button hidden after successful retry');
      
    } else {
      // Check for failed status indicator
      const failedIndicator = messageElement.locator('svg[class*="red"], [class*="failed"], [class*="alert"]');
      const hasFailedIndicator = await failedIndicator.isVisible();
      
      if (hasFailedIndicator) {
        console.log('✓ Failed status indicator visible');
      } else {
        console.log('⚠ No failure indication visible - may need implementation');
      }
    }
  });

  test('should not show status indicators for received messages', async ({ browser }) => {
    // Create two browser contexts
    const context1 = await browser.newContext();
    const context2 = await browser.newContext();
    
    const page1 = await context1.newPage();
    const page2 = await context2.newPage();
    
    const dashboard1 = new DashboardPage(page1);
    const dashboard2 = new DashboardPage(page2);
    
    // Login both users
    const testUser1 = testDataManager.getTestUser('primary');
    const testUser2 = testDataManager.getTestUser('secondary');
    
    await page1.goto('/login');
    await page1.fill('[data-testid="email-input"]', testUser1.email);
    await page1.fill('[data-testid="password-input"]', testUser1.password);
    await page1.click('[data-testid="login-button"]');
    await dashboard1.waitForPageLoad();
    
    await page2.goto('/login');
    await page2.fill('[data-testid="email-input"]', testUser2.email);
    await page2.fill('[data-testid="password-input"]', testUser2.password);
    await page2.click('[data-testid="login-button"]');
    await dashboard2.waitForPageLoad();
    
    // User 1 creates conversation and sends message
    await dashboard1.createDirectConversation(testUser2.username);
    await dashboard1.sendMessage('Test message from User 1');
    
    // User 2 should see the conversation and message
    await page2.waitForTimeout(3000);
    const conversationCount2 = await dashboard2.getConversationCount();
    
    if (conversationCount2 > 0) {
      await dashboard2.selectConversation(0);
      
      // Wait for message to appear
      await expect(async () => {
        const messageCount = await dashboard2.getMessageCount();
        expect(messageCount).toBeGreaterThan(0);
      }).toPass({ timeout: 10000 });
      
      // Check that received message does NOT have status indicators
      const messageElement2 = page2.locator('[data-testid="message"]').last();
      const statusIndicator2 = messageElement2.locator('[data-testid="message-status"], .message-status');
      
      const hasStatusIndicator = await statusIndicator2.isVisible();
      expect(hasStatusIndicator).toBe(false);
      console.log('✓ Received message correctly has no status indicator');
    }
    
    await context1.close();
    await context2.close();
  });
});
