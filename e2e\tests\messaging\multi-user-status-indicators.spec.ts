import { test, expect, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON> } from '@playwright/test';
import { DashboardPage } from '../../page-objects/DashboardPage';
import { TestDataManager } from '../../utils/test-data-manager';

test.describe('Multi-User Message Status Indicators', () => {
  let testDataManager: TestDataManager;
  let browser1: Browser;
  let browser2: Browser;
  let context1: BrowserContext;
  let context2: BrowserContext;
  let page1: Page;
  let page2: Page;
  let dashboard1: DashboardPage;
  let dashboard2: DashboardPage;

  test.beforeEach(async ({ browser }) => {
    testDataManager = new TestDataManager();
    
    // Create two separate browser contexts for two different users
    browser1 = browser;
    browser2 = browser;
    
    context1 = await browser1.newContext();
    context2 = await browser2.newContext();
    
    page1 = await context1.newPage();
    page2 = await context2.newPage();
    
    dashboard1 = new DashboardPage(page1);
    dashboard2 = new DashboardPage(page2);
    
    // Set up console logging for both pages
    page1.on('console', msg => {
      const text = msg.text();
      if (text.includes('🔴') || text.includes('🔵') || text.includes('🟢') || text.includes('🟡') || text.includes('🔶')) {
        console.log(`[USER1] ${text}`);
      }
    });
    
    page2.on('console', msg => {
      const text = msg.text();
      if (text.includes('🔴') || text.includes('🔵') || text.includes('🟢') || text.includes('🟡') || text.includes('🔶')) {
        console.log(`[USER2] ${text}`);
      }
    });
  });

  test.afterEach(async () => {
    await context1?.close();
    await context2?.close();
  });

  test('should show complete message status progression from sender to receiver', async () => {
    const testUser1 = testDataManager.getTestUser('primary');
    const testUser2 = testDataManager.getTestUser('secondary');
    
    console.log('=== LOGGING IN USER 1 ===');
    // Login User 1 (sender)
    await page1.goto('/login');
    await page1.fill('[data-testid="email-input"]', testUser1.email);
    await page1.fill('[data-testid="password-input"]', testUser1.password);
    await page1.click('[data-testid="login-button"]');
    await dashboard1.waitForPageLoad();
    
    console.log('=== LOGGING IN USER 2 ===');
    // Login User 2 (receiver)
    await page2.goto('/login');
    await page2.fill('[data-testid="email-input"]', testUser2.email);
    await page2.fill('[data-testid="password-input"]', testUser2.password);
    await page2.click('[data-testid="login-button"]');
    await dashboard2.waitForPageLoad();
    
    console.log('=== CREATING CONVERSATION ===');
    // User 1 creates conversation with User 2
    await dashboard1.createDirectConversation(testUser2.username);

    // Wait for conversation to be created
    await page1.waitForTimeout(3000);

    console.log('=== USER 2 JOINING CONVERSATION ===');
    // User 2 needs to navigate to the conversation
    // First, check if conversation appears in User 2's conversation list
    await page2.reload(); // Refresh to load conversations
    await dashboard2.waitForPageLoad();

    // Look for the conversation with User 1 in User 2's conversation list
    const conversationWithUser1 = page2.locator(`[data-testid="conversation"]:has-text("${testUser1.username}"), .conversation:has-text("${testUser1.username}")`);

    if (await conversationWithUser1.isVisible({ timeout: 5000 })) {
      console.log('✓ User 2 sees conversation with User 1');
      await conversationWithUser1.click();
      await dashboard2.waitForChatAreaLoad();
    } else {
      console.log('⚠ User 2 does not see conversation, creating new one');
      await dashboard2.createDirectConversation(testUser1.username);
    }

    // Wait for both users to be in the conversation
    await page1.waitForTimeout(2000);
    await page2.waitForTimeout(2000);
    
    console.log('=== SENDING MESSAGE FROM USER 1 ===');
    // User 1 sends a message
    const testMessage = `Multi-user test message ${Date.now()}`;
    await dashboard1.sendMessage(testMessage);
    
    console.log('=== CHECKING SENDER STATUS (USER 1) ===');
    // Check sender's view - should show "Sending..." then single checkmark
    await page1.waitForTimeout(2000);
    
    // Get the last message element from User 1's view
    const user1Messages = page1.locator('[data-testid="message"]');
    const user1LastMessage = user1Messages.last();
    
    // Check for status indicator in sender's view
    const user1StatusIndicator = user1LastMessage.locator('.lucide-check, .lucide-check-check, .status-indicator');
    await expect(user1StatusIndicator).toBeVisible({ timeout: 10000 });
    
    console.log('✓ User 1 (sender) sees status indicator');
    
    console.log('=== CHECKING MESSAGE DELIVERY TO USER 2 ===');
    // User 2 should receive the message
    await page2.waitForTimeout(3000);
    
    // Check if User 2 received the message
    const user2Messages = page2.locator('[data-testid="message"]');
    const user2MessageCount = await user2Messages.count();
    console.log('User 2 message count:', user2MessageCount);
    
    if (user2MessageCount > 0) {
      const user2LastMessage = user2Messages.last();
      const user2MessageText = await user2LastMessage.textContent();
      console.log('User 2 last message:', user2MessageText);
      
      // Verify User 2 received the correct message
      expect(user2MessageText).toContain(testMessage);
      console.log('✓ User 2 received the message');
      
      console.log('=== CHECKING DELIVERED STATUS UPDATE ===');
      // Wait for delivered status to propagate back to User 1
      await page1.waitForTimeout(3000);
      
      // Check if User 1's status indicator updated to show "delivered"
      const user1UpdatedStatusIndicator = user1LastMessage.locator('.lucide-check, .lucide-check-check, .status-indicator');
      const statusClasses = await user1UpdatedStatusIndicator.getAttribute('class');
      console.log('User 1 status indicator classes after delivery:', statusClasses);
      
      console.log('=== SIMULATING MESSAGE READ BY USER 2 ===');
      // Simulate User 2 reading the message (scroll to it, focus on it)
      await user2LastMessage.scrollIntoViewIfNeeded();
      await user2LastMessage.click();
      await page2.waitForTimeout(2000);
      
      console.log('=== CHECKING READ STATUS UPDATE ===');
      // Wait for read status to propagate back to User 1
      await page1.waitForTimeout(3000);
      
      // Check if User 1's status indicator updated to show "read"
      const user1FinalStatusIndicator = user1LastMessage.locator('.lucide-check, .lucide-check-check, .status-indicator');
      const finalStatusClasses = await user1FinalStatusIndicator.getAttribute('class');
      console.log('User 1 status indicator classes after read:', finalStatusClasses);
      
      // Verify status progression
      console.log('✓ Complete status progression test completed');
      
    } else {
      console.log('❌ User 2 did not receive the message');
      throw new Error('Message was not delivered to User 2');
    }
  });

  test('should handle multiple messages with correct status indicators', async () => {
    const testUser1 = testDataManager.getTestUser('primary');
    const testUser2 = testDataManager.getTestUser('secondary');
    
    // Login both users
    await page1.goto('/login');
    await page1.fill('[data-testid="email-input"]', testUser1.email);
    await page1.fill('[data-testid="password-input"]', testUser1.password);
    await page1.click('[data-testid="login-button"]');
    await dashboard1.waitForPageLoad();
    
    await page2.goto('/login');
    await page2.fill('[data-testid="email-input"]', testUser2.email);
    await page2.fill('[data-testid="password-input"]', testUser2.password);
    await page2.click('[data-testid="login-button"]');
    await dashboard2.waitForPageLoad();
    
    // Create conversation
    await dashboard1.createDirectConversation(testUser2.username);
    await page2.waitForTimeout(2000);
    
    // Send multiple messages
    const messages = [
      `First message ${Date.now()}`,
      `Second message ${Date.now() + 1}`,
      `Third message ${Date.now() + 2}`
    ];
    
    for (const message of messages) {
      console.log(`Sending: ${message}`);
      await dashboard1.sendMessage(message);
      await page1.waitForTimeout(1000);
    }
    
    // Wait for all messages to be processed
    await page1.waitForTimeout(5000);
    await page2.waitForTimeout(5000);
    
    // Check that all messages have status indicators
    const user1Messages = page1.locator('[data-testid="message"]');
    const messageCount = await user1Messages.count();
    console.log('Total messages sent:', messageCount);
    
    // Check each message for status indicators
    for (let i = 0; i < messageCount; i++) {
      const message = user1Messages.nth(i);
      const statusIndicator = message.locator('.lucide-check, .lucide-check-check, .status-indicator');
      
      if (await statusIndicator.isVisible()) {
        console.log(`✓ Message ${i + 1} has status indicator`);
      } else {
        console.log(`❌ Message ${i + 1} missing status indicator`);
      }
    }
    
    console.log('✓ Multiple message status test completed');
  });
});
