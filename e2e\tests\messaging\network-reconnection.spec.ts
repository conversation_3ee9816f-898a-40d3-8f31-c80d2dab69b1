import { test, expect } from '@playwright/test';
import { DashboardPage } from '../../page-objects/DashboardPage';
import { TestDataManager } from '../../utils/test-data-manager';

test.describe('Network Reconnection and Message Deduplication Tests', () => {
  let testDataManager: TestDataManager;

  test.beforeEach(async () => {
    testDataManager = new TestDataManager();
  });

  test('should not duplicate messages during network disconnection and reconnection', async ({ page }) => {
    const dashboard = new DashboardPage(page);

    // Capture console logs for debugging
    const consoleLogs: string[] = [];
    page.on('console', msg => {
      const text = msg.text();
      consoleLogs.push(`[${msg.type()}] ${text}`);
      if (text.includes('🔴') || text.includes('🔵') || text.includes('🟢') || text.includes('🟡') || text.includes('🔶')) {
        console.log(text);
      }
    });

    // Login
    const testUser1 = testDataManager.getTestUser('primary');
    const testUser2 = testDataManager.getTestUser('secondary');

    await page.goto('/login');
    await page.fill('[data-testid="email-input"]', testUser1.email);
    await page.fill('[data-testid="password-input"]', testUser1.password);
    await page.click('[data-testid="login-button"]');
    await dashboard.waitForPageLoad();

    // Create conversation and send initial message to establish real conversation
    await dashboard.createDirectConversation(testUser2.username);
    await dashboard.sendMessage('Initial message to establish conversation');
    await page.waitForTimeout(3000); // Wait for conversation to be fully established
    
    // Send a message while connected
    await dashboard.sendMessage('Message before disconnect');
    await page.waitForTimeout(2000);
    
    // Get initial message count
    const initialCount = await dashboard.getMessageCount();
    console.log('Initial message count:', initialCount);
    
    // Simulate network disconnection
    console.log('Simulating network disconnection...');
    await page.route('**/socket.io/**', route => route.abort());
    
    // Wait for disconnection to be detected
    await page.waitForTimeout(2000);
    
    // Check what the connection status actually shows
    const connectionText = await dashboard.connectionStatus.textContent();
    console.log('Connection status after disconnect:', connectionText);

    // Verify connection status shows disconnected (may take time to detect)
    try {
      await expect(dashboard.connectionStatus).toContainText(/reconnecting|connecting|disconnected/i, { timeout: 10000 });
      console.log('✓ Connection status shows disconnected');
    } catch (error) {
      console.log('⚠ Connection status did not change to disconnected, continuing test...');
      console.log('Current status:', await dashboard.connectionStatus.textContent());
    }
    
    // Try to send a message while disconnected
    const disconnectedMessage = 'Message during disconnect';
    await dashboard.messageInput.fill(disconnectedMessage);
    await dashboard.sendButton.click();
    
    // Message should appear optimistically
    await page.waitForTimeout(1000);
    const countAfterDisconnectedSend = await dashboard.getMessageCount();
    console.log('Message count after disconnected send:', countAfterDisconnectedSend);
    
    // Restore network connection
    console.log('Restoring network connection...');
    await page.unroute('**/socket.io/**');
    
    // Wait for reconnection
    await expect(dashboard.connectionStatus).toContainText(/connected/i, { timeout: 15000 });
    console.log('✓ Connection restored');
    
    // Wait for any message synchronization
    await page.waitForTimeout(5000);
    
    // Check final message count - should not have duplicates
    const finalCount = await dashboard.getMessageCount();
    console.log('Final message count:', finalCount);
    
    // Verify no duplicate messages
    expect(finalCount).toBe(countAfterDisconnectedSend);
    
    // Verify message content - check for duplicates
    const allMessages = await dashboard.getAllMessages();
    const messageTexts = allMessages.map(msg => msg.toLowerCase());
    
    // Count occurrences of each message
    const messageCounts = messageTexts.reduce((acc, text) => {
      acc[text] = (acc[text] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    
    // Check for duplicates
    const duplicates = Object.entries(messageCounts).filter(([_, count]) => count > 1);
    
    if (duplicates.length > 0) {
      console.log('❌ Found duplicate messages:', duplicates);
      expect(duplicates.length).toBe(0);
    } else {
      console.log('✓ No duplicate messages found');
    }
    
    // Verify the disconnected message appears exactly once
    const disconnectedMessageCount = messageTexts.filter(text =>
      text.includes(disconnectedMessage.toLowerCase())
    ).length;
    expect(disconnectedMessageCount).toBe(1);
    console.log('✓ Disconnected message appears exactly once');

    // Print relevant console logs for debugging
    console.log('\n=== RELEVANT CONSOLE LOGS ===');
    const relevantLogs = consoleLogs.filter(log =>
      log.includes('🔴') || log.includes('🔵') || log.includes('🟢') || log.includes('🟡') || log.includes('🔶')
    );
    relevantLogs.forEach((log, index) => {
      console.log(`${index + 1}: ${log}`);
    });
    console.log('=== END CONSOLE LOGS ===\n');
  });

  test('should handle multiple reconnection cycles without duplicating messages', async ({ page }) => {
    const dashboard = new DashboardPage(page);
    
    // Login
    const testUser1 = testDataManager.getTestUser('primary');
    const testUser2 = testDataManager.getTestUser('secondary');
    
    await page.goto('/login');
    await page.fill('[data-testid="email-input"]', testUser1.email);
    await page.fill('[data-testid="password-input"]', testUser1.password);
    await page.click('[data-testid="login-button"]');
    await dashboard.waitForPageLoad();
    
    // Create conversation
    await dashboard.createDirectConversation(testUser2.username);
    
    const testMessages = [
      'Message 1 - before first disconnect',
      'Message 2 - during first disconnect',
      'Message 3 - after first reconnect',
      'Message 4 - during second disconnect',
      'Message 5 - after final reconnect'
    ];
    
    let messageIndex = 0;
    
    // Send first message while connected
    await dashboard.sendMessage(testMessages[messageIndex++]);
    await page.waitForTimeout(1000);
    
    // First disconnect/reconnect cycle
    console.log('First disconnect cycle...');
    await page.route('**/socket.io/**', route => route.abort());
    await page.waitForTimeout(2000);
    
    // Send message during disconnect
    await dashboard.sendMessage(testMessages[messageIndex++]);
    await page.waitForTimeout(1000);
    
    // Reconnect
    await page.unroute('**/socket.io/**');
    await expect(dashboard.connectionStatus).toContainText(/connected/i, { timeout: 15000 });
    await page.waitForTimeout(2000);
    
    // Send message after reconnect
    await dashboard.sendMessage(testMessages[messageIndex++]);
    await page.waitForTimeout(1000);
    
    // Second disconnect/reconnect cycle
    console.log('Second disconnect cycle...');
    await page.route('**/socket.io/**', route => route.abort());
    await page.waitForTimeout(2000);
    
    // Send message during second disconnect
    await dashboard.sendMessage(testMessages[messageIndex++]);
    await page.waitForTimeout(1000);
    
    // Final reconnect
    await page.unroute('**/socket.io/**');
    await expect(dashboard.connectionStatus).toContainText(/connected/i, { timeout: 15000 });
    await page.waitForTimeout(2000);
    
    // Send final message
    await dashboard.sendMessage(testMessages[messageIndex++]);
    await page.waitForTimeout(3000);
    
    // Verify all messages appear exactly once
    const finalCount = await dashboard.getMessageCount();
    console.log('Final message count:', finalCount);
    
    const allMessages = await dashboard.getAllMessages();
    console.log('All messages:', allMessages);
    
    // Check each test message appears exactly once
    for (const testMessage of testMessages) {
      const occurrences = allMessages.filter(msg => 
        msg.toLowerCase().includes(testMessage.toLowerCase())
      ).length;
      
      expect(occurrences).toBe(1);
      console.log(`✓ "${testMessage}" appears exactly once`);
    }
  });

  test('should maintain message order during reconnection', async ({ page }) => {
    const dashboard = new DashboardPage(page);
    
    // Login
    const testUser1 = testDataManager.getTestUser('primary');
    const testUser2 = testDataManager.getTestUser('secondary');
    
    await page.goto('/login');
    await page.fill('[data-testid="email-input"]', testUser1.email);
    await page.fill('[data-testid="password-input"]', testUser1.password);
    await page.click('[data-testid="login-button"]');
    await dashboard.waitForPageLoad();
    
    // Create conversation
    await dashboard.createDirectConversation(testUser2.username);
    
    const orderedMessages = [
      'First message',
      'Second message', 
      'Third message (during disconnect)',
      'Fourth message (during disconnect)',
      'Fifth message (after reconnect)'
    ];
    
    // Send first two messages while connected
    await dashboard.sendMessage(orderedMessages[0]);
    await page.waitForTimeout(500);
    await dashboard.sendMessage(orderedMessages[1]);
    await page.waitForTimeout(1000);
    
    // Disconnect
    await page.route('**/socket.io/**', route => route.abort());
    await page.waitForTimeout(2000);
    
    // Send messages during disconnect
    await dashboard.sendMessage(orderedMessages[2]);
    await page.waitForTimeout(500);
    await dashboard.sendMessage(orderedMessages[3]);
    await page.waitForTimeout(1000);
    
    // Reconnect
    await page.unroute('**/socket.io/**');
    await expect(dashboard.connectionStatus).toContainText(/connected/i, { timeout: 15000 });
    await page.waitForTimeout(2000);
    
    // Send final message
    await dashboard.sendMessage(orderedMessages[4]);
    await page.waitForTimeout(3000);
    
    // Verify message order is maintained
    const allMessages = await dashboard.getAllMessages();
    console.log('Messages in order:', allMessages);
    
    // Check that our test messages appear in the correct order
    let lastFoundIndex = -1;
    for (const expectedMessage of orderedMessages) {
      const foundIndex = allMessages.findIndex((msg, index) => 
        index > lastFoundIndex && msg.toLowerCase().includes(expectedMessage.toLowerCase())
      );
      
      expect(foundIndex).toBeGreaterThan(lastFoundIndex);
      lastFoundIndex = foundIndex;
      console.log(`✓ "${expectedMessage}" found at correct position ${foundIndex}`);
    }
  });

  test('should handle rapid message sending during unstable connection', async ({ page }) => {
    const dashboard = new DashboardPage(page);
    
    // Login
    const testUser1 = testDataManager.getTestUser('primary');
    const testUser2 = testDataManager.getTestUser('secondary');
    
    await page.goto('/login');
    await page.fill('[data-testid="email-input"]', testUser1.email);
    await page.fill('[data-testid="password-input"]', testUser1.password);
    await page.click('[data-testid="login-button"]');
    await dashboard.waitForPageLoad();
    
    // Create conversation
    await dashboard.createDirectConversation(testUser2.username);
    
    const rapidMessages = [
      'Rapid 1',
      'Rapid 2',
      'Rapid 3',
      'Rapid 4',
      'Rapid 5'
    ];
    
    // Send messages rapidly while simulating unstable connection
    for (let i = 0; i < rapidMessages.length; i++) {
      const message = rapidMessages[i];
      
      // Randomly disconnect/reconnect during sending
      if (i === 2) {
        // Disconnect during middle of rapid sending
        await page.route('**/socket.io/**', route => route.abort());
        console.log('Disconnected during rapid sending');
      }
      
      await dashboard.sendMessage(message);
      
      if (i === 3) {
        // Reconnect
        await page.unroute('**/socket.io/**');
        console.log('Reconnected during rapid sending');
      }
      
      // Small delay between messages
      await page.waitForTimeout(200);
    }
    
    // Wait for connection to stabilize
    await expect(dashboard.connectionStatus).toContainText(/connected/i, { timeout: 15000 });
    await page.waitForTimeout(5000);
    
    // Verify all messages appear exactly once
    const allMessages = await dashboard.getAllMessages();
    
    for (const expectedMessage of rapidMessages) {
      const occurrences = allMessages.filter(msg => 
        msg.toLowerCase().includes(expectedMessage.toLowerCase())
      ).length;
      
      expect(occurrences).toBe(1);
      console.log(`✓ "${expectedMessage}" appears exactly once`);
    }
    
    console.log('✓ All rapid messages handled correctly during unstable connection');
  });

  test('should preserve message status during reconnection', async ({ page }) => {
    const dashboard = new DashboardPage(page);
    
    // Login
    const testUser1 = testDataManager.getTestUser('primary');
    const testUser2 = testDataManager.getTestUser('secondary');
    
    await page.goto('/login');
    await page.fill('[data-testid="email-input"]', testUser1.email);
    await page.fill('[data-testid="password-input"]', testUser1.password);
    await page.click('[data-testid="login-button"]');
    await dashboard.waitForPageLoad();
    
    // Create conversation
    await dashboard.createDirectConversation(testUser2.username);
    
    // Send a message while connected
    await dashboard.sendMessage('Message with status');
    await page.waitForTimeout(2000);
    
    // Check initial status
    const messageElement = page.locator('[data-testid="message"]').last();
    const initialStatusVisible = await messageElement.locator('[data-testid="message-status"], .message-status, svg').isVisible();
    console.log('Initial status indicator visible:', initialStatusVisible);
    
    // Disconnect
    await page.route('**/socket.io/**', route => route.abort());
    await page.waitForTimeout(2000);
    
    // Reconnect
    await page.unroute('**/socket.io/**');
    await expect(dashboard.connectionStatus).toContainText(/connected/i, { timeout: 15000 });
    await page.waitForTimeout(3000);
    
    // Check status after reconnection
    const finalStatusVisible = await messageElement.locator('[data-testid="message-status"], .message-status, svg').isVisible();
    console.log('Status indicator visible after reconnection:', finalStatusVisible);
    
    // Status should be preserved (or restored)
    if (initialStatusVisible) {
      expect(finalStatusVisible).toBe(true);
      console.log('✓ Message status preserved during reconnection');
    } else {
      console.log('ℹ No initial status to preserve');
    }
  });
});
