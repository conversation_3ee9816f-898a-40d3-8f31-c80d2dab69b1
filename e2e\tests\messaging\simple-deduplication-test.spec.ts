import { test, expect } from '@playwright/test';
import { DashboardPage } from '../../page-objects/DashboardPage';
import { TestDataManager } from '../../utils/test-data-manager';

test.describe('Simple Message Deduplication Test', () => {
  let testDataManager: TestDataManager;

  test.beforeEach(async () => {
    testDataManager = new TestDataManager();
  });

  test('should not create duplicate messages in a fresh conversation', async ({ page }) => {
    const dashboard = new DashboardPage(page);

    // Capture console logs for debugging
    const consoleLogs: string[] = [];
    page.on('console', msg => {
      const text = msg.text();
      consoleLogs.push(`[${msg.type()}] ${text}`);
      if (text.includes('🔴') || text.includes('🔵') || text.includes('🟢') || text.includes('🟡') || text.includes('🔶')) {
        console.log(text);
      }
    });

    // Login
    const testUser1 = testDataManager.getTestUser('primary');
    const testUser2 = testDataManager.getTestUser('secondary');
    
    await page.goto('/login');
    await page.fill('[data-testid="email-input"]', testUser1.email);
    await page.fill('[data-testid="password-input"]', testUser1.password);
    await page.click('[data-testid="login-button"]');
    await dashboard.waitForPageLoad();
    
    // Create conversation with existing test user
    await dashboard.createDirectConversation(testUser2.username);
    
    // Count initial messages
    const initialCount = await dashboard.getMessageCount();
    console.log('Initial message count:', initialCount);

    // Send a unique test message
    const testMessage = `Test message ${Date.now()}`;
    await dashboard.sendMessage(testMessage);

    // Wait for message to be processed
    await page.waitForTimeout(3000);

    // Count final messages
    const finalCount = await dashboard.getMessageCount();
    console.log('Final message count:', finalCount);

    // Check that exactly one message was added
    console.log('Expected final count:', initialCount + 1);
    console.log('Actual final count:', finalCount);
    expect(finalCount).toBe(initialCount + 1);

    // Get all message texts and check for duplicates
    const messageTexts = await dashboard.getAllMessages();

    // Count occurrences of our test message
    const testMessageCount = messageTexts.filter(text =>
      text && text.includes(testMessage)
    ).length;
    
    console.log('Test message occurrences:', testMessageCount);
    console.log('All message texts:', messageTexts);
    
    // Verify our test message appears exactly once
    expect(testMessageCount).toBe(1);
    
    // Print relevant console logs for debugging
    console.log('\n=== RELEVANT CONSOLE LOGS ===');
    const relevantLogs = consoleLogs.filter(log => 
      log.includes('🔴') || log.includes('🔵') || log.includes('🟢') || log.includes('🟡') || log.includes('🔶')
    );
    relevantLogs.forEach((log, index) => {
      console.log(`${index + 1}: ${log}`);
    });
    console.log('=== END CONSOLE LOGS ===\n');
  });
});
