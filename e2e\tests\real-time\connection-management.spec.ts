import { test, expect } from '../../fixtures/test-fixtures';
import { TestUtils } from '../../fixtures/test-fixtures';
import { DashboardPage } from '../../page-objects/DashboardPage';

test.describe('Socket Connection Management', () => {
  test('should establish socket connection on login', async ({ loginPage, dashboardPage, testDataManager }) => {
    const testUser = testDataManager.getTestUser('primary');
    
    await loginPage.goto();
    await loginPage.login(testUser.email, testUser.password);
    await loginPage.waitForLoginSuccess();
    
    // Verify socket connection is established
    await dashboardPage.waitForConnectionEstablished();
    expect(await dashboardPage.isConnected()).toBe(true);
  });

  test('should maintain connection during normal usage', async ({ dashboardPage, multiUserPages }) => {
    const { user1, user2 } = multiUserPages;
    
    // Verify initial connection
    expect(await user1.dashboard.isConnected()).toBe(true);
    expect(await user2.dashboard.isConnected()).toBe(true);
    
    // Perform various actions and verify connection remains stable
    await TestUtils.setupConversation(user1.dashboard, 'testuser2');
    expect(await user1.dashboard.isConnected()).toBe(true);
    
    await user1.dashboard.sendMessage('Connection test message');
    await user1.dashboard.waitForMessageSent();
    expect(await user1.dashboard.isConnected()).toBe(true);
    
    await user2.dashboard.waitForNewMessage();
    expect(await user2.dashboard.isConnected()).toBe(true);
  });

  test('should handle connection interruption and recovery', async ({ multiUserPages }) => {
    const { user1, user2 } = multiUserPages;
    
    await TestUtils.setupConversation(user1.dashboard, 'testuser2');
    
    // Verify initial connection
    expect(await user1.dashboard.isConnected()).toBe(true);
    
    // Simulate network interruption
    await TestUtils.simulateNetworkFailure(user1.page);
    
    // Wait for disconnection detection
    await user1.dashboard.waitForDisconnection();
    expect(await user1.dashboard.isConnected()).toBe(false);
    
    // Restore network
    await TestUtils.restoreNetwork(user1.page);
    
    // Wait for automatic reconnection
    await user1.dashboard.waitForConnectionEstablished();
    expect(await user1.dashboard.isConnected()).toBe(true);
    
    // Verify functionality works after reconnection
    await user1.dashboard.sendMessage('Message after reconnection');
    await user2.dashboard.waitForNewMessage();
    
    const lastMessage = await user2.dashboard.getLastMessage();
    expect(lastMessage).toContain('Message after reconnection');
  });

  test('should handle server restart scenario', async ({ multiUserPages }) => {
    const { user1, user2 } = multiUserPages;
    
    await TestUtils.setupConversation(user1.dashboard, 'testuser2');
    
    // Send initial message to establish communication
    await user1.dashboard.sendMessage('Before server restart');
    await user2.dashboard.waitForNewMessage();
    
    // Simulate server unavailability
    await user1.page.route('**/socket.io/**', route => route.abort());
    await user2.page.route('**/socket.io/**', route => route.abort());
    
    // Wait for disconnection
    await user1.dashboard.waitForDisconnection();
    await user2.dashboard.waitForDisconnection();
    
    // Restore server connection
    await user1.page.unroute('**/socket.io/**');
    await user2.page.unroute('**/socket.io/**');
    
    // Wait for reconnection
    await user1.dashboard.waitForConnectionEstablished();
    await user2.dashboard.waitForConnectionEstablished();
    
    // Verify communication works after server restart
    await user1.dashboard.sendMessage('After server restart');
    await user2.dashboard.waitForNewMessage();
    
    const lastMessage = await user2.dashboard.getLastMessage();
    expect(lastMessage).toContain('After server restart');
  });

  test('should handle multiple connection attempts', async ({ multiUserPages }) => {
    const { user1 } = multiUserPages;
    
    // Simulate unstable connection with multiple interruptions
    for (let i = 0; i < 3; i++) {
      // Interrupt connection
      await TestUtils.simulateNetworkFailure(user1.page);
      await user1.dashboard.waitForDisconnection();
      
      // Restore connection
      await TestUtils.restoreNetwork(user1.page);
      await user1.dashboard.waitForConnectionEstablished();
      
      expect(await user1.dashboard.isConnected()).toBe(true);
      
      // Small delay between cycles
      await user1.page.waitForTimeout(1000);
    }
  });

  test('should handle connection timeout scenarios', async ({ multiUserPages }) => {
    const { user1 } = multiUserPages;
    
    // Simulate slow network that causes timeouts
    await TestUtils.simulateSlowNetwork(user1.page, 5000);
    
    // Try to send a message during slow network
    await user1.dashboard.sendMessage('Timeout test message');
    
    // Connection should eventually timeout and show disconnected state
    // The exact behavior depends on implementation
    await user1.page.waitForTimeout(10000);
    
    // Restore normal network
    await TestUtils.restoreNetwork(user1.page);
    await user1.dashboard.waitForConnectionEstablished();
    
    expect(await user1.dashboard.isConnected()).toBe(true);
  });

  test('should maintain connection across browser tab switches', async ({ multiUserPages }) => {
    const { user1 } = multiUserPages;
    
    // Open new tab
    const newTab = await user1.page.context().newPage();
    await newTab.goto('https://example.com');
    
    // Switch back to original tab
    await user1.page.bringToFront();
    
    // Connection should still be active
    expect(await user1.dashboard.isConnected()).toBe(true);
    
    // Verify functionality still works
    await user1.dashboard.sendMessage('Tab switch test');
    await user1.dashboard.waitForMessageSent();
    
    await newTab.close();
  });

  test('should handle connection during page visibility changes', async ({ multiUserPages }) => {
    const { user1, user2 } = multiUserPages;
    
    await TestUtils.setupConversation(user1.dashboard, 'testuser2');
    
    // Simulate page becoming hidden (user switches to another app)
    await user1.page.evaluate(() => {
      Object.defineProperty(document, 'visibilityState', {
        writable: true,
        value: 'hidden'
      });
      document.dispatchEvent(new Event('visibilitychange'));
    });
    
    // Wait a moment
    await user1.page.waitForTimeout(2000);
    
    // Simulate page becoming visible again
    await user1.page.evaluate(() => {
      Object.defineProperty(document, 'visibilityState', {
        writable: true,
        value: 'visible'
      });
      document.dispatchEvent(new Event('visibilitychange'));
    });
    
    // Connection should still be active
    expect(await user1.dashboard.isConnected()).toBe(true);
    
    // Verify messaging still works
    await user1.dashboard.sendMessage('Visibility test message');
    await user2.dashboard.waitForNewMessage();
  });

  test('should handle connection with authentication token refresh', async ({ multiUserPages }) => {
    const { user1 } = multiUserPages;
    
    // Simulate token expiration by intercepting auth requests
    await user1.page.route('**/api/auth/**', route => {
      if (route.request().url().includes('refresh')) {
        route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            success: true,
            data: { tokens: { access: 'new-token', refresh: 'new-refresh-token' } }
          })
        });
      } else {
        route.continue();
      }
    });
    
    // Connection should handle token refresh gracefully
    expect(await user1.dashboard.isConnected()).toBe(true);
    
    // Verify functionality continues to work
    await user1.dashboard.sendMessage('Token refresh test');
    await user1.dashboard.waitForMessageSent();
  });

  test('should handle multiple users connecting simultaneously', async ({ browser }) => {
    // Create multiple browser contexts
    const contexts = await Promise.all([
      browser.newContext(),
      browser.newContext(),
      browser.newContext()
    ]);
    
    try {
      // Authenticate all contexts
      await Promise.all(contexts.map(async (context, index) => {
        await TestUtils.authenticateUser(context, index === 0 ? 'primary' : index === 1 ? 'secondary' : 'tertiary');
      }));
      
      // Create pages and navigate to dashboard
      const pages = await Promise.all(contexts.map(context => context.newPage()));

      // Create dashboard instances for each page
      const dashboards = pages.map(page => ({ page, dashboard: new DashboardPage(page) }));
      
      // Navigate all to dashboard
      await Promise.all(dashboards.map(({ dashboard }) => dashboard.goto()));
      
      // Verify all connections are established
      await Promise.all(dashboards.map(async ({ dashboard }) => {
        await dashboard.waitForConnectionEstablished();
        expect(await dashboard.isConnected()).toBe(true);
      }));
      
    } finally {
      // Clean up contexts
      await Promise.all(contexts.map(context => context.close()));
    }
  });

  test('should handle connection during browser refresh', async ({ multiUserPages }) => {
    const { user1, user2 } = multiUserPages;
    
    await TestUtils.setupConversation(user1.dashboard, 'testuser2');
    
    // Send message before refresh
    await user1.dashboard.sendMessage('Before refresh');
    await user2.dashboard.waitForNewMessage();
    
    // Refresh user1's browser
    await user1.page.reload();
    await user1.dashboard.waitForPageLoad();
    
    // Connection should be re-established
    await user1.dashboard.waitForConnectionEstablished();
    expect(await user1.dashboard.isConnected()).toBe(true);
    
    // Verify messaging works after refresh
    await user1.dashboard.sendMessage('After refresh');
    await user2.dashboard.waitForNewMessage();
    
    const lastMessage = await user2.dashboard.getLastMessage();
    expect(lastMessage).toContain('After refresh');
  });

  test('should handle connection errors gracefully', async ({ multiUserPages }) => {
    const { user1 } = multiUserPages;
    
    // Simulate various connection errors
    const errorScenarios = [
      { status: 500, description: 'Server Error' },
      { status: 503, description: 'Service Unavailable' },
      { status: 404, description: 'Not Found' }
    ];
    
    for (const scenario of errorScenarios) {
      // Simulate error response
      await user1.page.route('**/socket.io/**', route => {
        route.fulfill({
          status: scenario.status,
          body: `${scenario.description}`
        });
      });
      
      // Wait for error handling
      await user1.page.waitForTimeout(2000);
      
      // Restore normal connection
      await user1.page.unroute('**/socket.io/**');
      
      // Wait for recovery
      await user1.dashboard.waitForConnectionEstablished();
      expect(await user1.dashboard.isConnected()).toBe(true);
    }
  });

  test('should display connection status to user', async ({ multiUserPages }) => {
    const { user1 } = multiUserPages;
    
    // Verify connected status is shown
    expect(await user1.dashboard.isConnected()).toBe(true);
    
    // Simulate disconnection
    await TestUtils.simulateNetworkFailure(user1.page);
    await user1.dashboard.waitForDisconnection();
    
    // Verify disconnected status is shown
    expect(await user1.dashboard.isConnected()).toBe(false);
    
    // Restore connection
    await TestUtils.restoreNetwork(user1.page);
    await user1.dashboard.waitForConnectionEstablished();
    
    // Verify connected status is restored
    expect(await user1.dashboard.isConnected()).toBe(true);
  });
});
