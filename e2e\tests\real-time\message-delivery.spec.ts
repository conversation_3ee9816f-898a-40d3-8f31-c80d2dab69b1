import { test, expect } from '../../fixtures/test-fixtures';
import { TestUtils } from '../../fixtures/test-fixtures';

test.describe('Real-time Message Delivery', () => {
  test('should deliver messages in real-time between two users', async ({ multiUserPages }) => {
    const { user1, user2 } = multiUserPages;
    
    // Create a conversation between users
    await TestUtils.setupConversation(user1.dashboard, 'testuser2');
    
    // User 1 sends a message
    const testMessage = 'Hello from user 1!';
    await user1.dashboard.sendMessage(testMessage);
    
    // Verify message appears for sender
    await user1.dashboard.waitForMessageSent();
    const senderLastMessage = await user1.dashboard.getLastMessage();
    expect(senderLastMessage).toContain(testMessage);
    
    // Verify message appears for receiver in real-time
    await user2.dashboard.waitForNewMessage();
    const receiverLastMessage = await user2.dashboard.getLastMessage();
    expect(receiverLastMessage).toContain(testMessage);
  });

  test('should maintain message order in real-time delivery', async ({ multiUserPages }) => {
    const { user1, user2 } = multiUserPages;
    
    await TestUtils.setupConversation(user1.dashboard, 'testuser2');
    
    // Send multiple messages in sequence
    const messages = ['Message 1', 'Message 2', 'Message 3'];
    
    for (const message of messages) {
      await user1.dashboard.sendMessage(message);
      await user1.dashboard.waitForMessageSent();
    }
    
    // Wait for all messages to be received
    await user2.dashboard.waitForNewMessage();
    
    // Verify message order is maintained
    const messageCount = await user2.dashboard.getMessageCount();
    expect(messageCount).toBeGreaterThanOrEqual(3);
    
    // Check the last three messages are in correct order
    for (let i = 0; i < messages.length; i++) {
      const messageIndex = messageCount - messages.length + i;
      const messageContent = await user2.dashboard.getMessageByIndex(messageIndex);
      expect(messageContent).toContain(messages[i]);
    }
  });

  test('should handle concurrent message sending', async ({ multiUserPages }) => {
    const { user1, user2 } = multiUserPages;
    
    await TestUtils.setupConversation(user1.dashboard, 'testuser2');
    
    // Send messages concurrently from both users
    const user1Message = 'Concurrent message from user 1';
    const user2Message = 'Concurrent message from user 2';
    
    await Promise.all([
      user1.dashboard.sendMessage(user1Message),
      user2.dashboard.sendMessage(user2Message)
    ]);
    
    // Wait for both messages to be delivered
    await user1.dashboard.waitForNewMessage();
    await user2.dashboard.waitForNewMessage();
    
    // Verify both users see both messages
    const user1Messages = await user1.dashboard.getMessageCount();
    const user2Messages = await user2.dashboard.getMessageCount();
    
    expect(user1Messages).toBeGreaterThanOrEqual(2);
    expect(user2Messages).toBeGreaterThanOrEqual(2);
  });

  test('should deliver messages with low latency', async ({ multiUserPages }) => {
    const { user1, user2 } = multiUserPages;
    
    await TestUtils.setupConversation(user1.dashboard, 'testuser2');
    
    // Measure message delivery time
    const startTime = Date.now();
    const testMessage = 'Latency test message';
    
    await user1.dashboard.sendMessage(testMessage);
    await user2.dashboard.waitForNewMessage();
    
    const endTime = Date.now();
    const latency = endTime - startTime;
    
    // Message should be delivered within 2 seconds
    expect(latency).toBeLessThan(2000);
    
    // Verify message content
    const receivedMessage = await user2.dashboard.getLastMessage();
    expect(receivedMessage).toContain(testMessage);
  });

  test('should handle message delivery during network interruption', async ({ multiUserPages }) => {
    const { user1, user2 } = multiUserPages;
    
    await TestUtils.setupConversation(user1.dashboard, 'testuser2');
    
    // Send initial message to establish connection
    await user1.dashboard.sendMessage('Initial message');
    await user2.dashboard.waitForNewMessage();
    
    // Simulate network interruption for user2
    await TestUtils.simulateNetworkIssue(user2.page, 2000);
    
    // User1 sends message during interruption
    const messagesDuringOutage = ['Message during outage 1', 'Message during outage 2'];
    
    for (const message of messagesDuringOutage) {
      await user1.dashboard.sendMessage(message);
      await user1.dashboard.waitForMessageSent();
    }
    
    // Wait for network recovery and message synchronization
    await user2.dashboard.waitForConnectionEstablished();
    await user2.dashboard.waitForNewMessage();
    
    // Verify all messages were eventually delivered
    const finalMessageCount = await user2.dashboard.getMessageCount();
    expect(finalMessageCount).toBeGreaterThanOrEqual(3); // Initial + 2 during outage
  });

  test('should support group message delivery', async ({ multiUserPages }) => {
    const { user1, user2, user3 } = multiUserPages;
    
    // Create group conversation (this would need to be implemented in the app)
    // For now, we'll test with multiple direct conversations
    
    await TestUtils.setupConversation(user1.dashboard, 'testuser2');
    await TestUtils.setupConversation(user1.dashboard, 'testuser3');
    
    const groupMessage = 'Message to multiple users';
    
    // Send message to both users
    await user1.dashboard.sendMessage(groupMessage);
    
    // Verify both users receive the message
    await user2.dashboard.waitForNewMessage();
    await user3.dashboard.waitForNewMessage();
    
    const user2Message = await user2.dashboard.getLastMessage();
    const user3Message = await user3.dashboard.getLastMessage();
    
    expect(user2Message).toContain(groupMessage);
    expect(user3Message).toContain(groupMessage);
  });

  test('should handle message delivery with special characters', async ({ multiUserPages }) => {
    const { user1, user2 } = multiUserPages;
    
    await TestUtils.setupConversation(user1.dashboard, 'testuser2');
    
    // Test various special characters and emojis
    const specialMessages = [
      'Message with emojis 😀🎉🚀',
      'Special chars: !@#$%^&*()_+-=[]{}|;:,.<>?',
      'Unicode: 测试 пример العربية',
      'HTML: <script>alert("test")</script>',
      'Newlines:\nLine 1\nLine 2'
    ];
    
    for (const message of specialMessages) {
      await user1.dashboard.sendMessage(message);
      await user2.dashboard.waitForNewMessage();
      
      const receivedMessage = await user2.dashboard.getLastMessage();
      expect(receivedMessage).toContain(message);
    }
  });

  test('should maintain connection status during message delivery', async ({ multiUserPages }) => {
    const { user1, user2 } = multiUserPages;
    
    // Verify both users are connected
    expect(await user1.dashboard.isConnected()).toBe(true);
    expect(await user2.dashboard.isConnected()).toBe(true);
    
    await TestUtils.setupConversation(user1.dashboard, 'testuser2');
    
    // Send messages and verify connection remains stable
    for (let i = 0; i < 5; i++) {
      await user1.dashboard.sendMessage(`Message ${i + 1}`);
      await user1.dashboard.waitForMessageSent();
      
      // Verify connection status remains stable
      expect(await user1.dashboard.isConnected()).toBe(true);
      expect(await user2.dashboard.isConnected()).toBe(true);
    }
  });

  test('should handle rapid message succession', async ({ multiUserPages }) => {
    const { user1, user2 } = multiUserPages;
    
    await TestUtils.setupConversation(user1.dashboard, 'testuser2');
    
    // Send messages rapidly
    const rapidMessages = Array.from({ length: 10 }, (_, i) => `Rapid message ${i + 1}`);
    
    // Send all messages quickly
    for (const message of rapidMessages) {
      await user1.dashboard.sendMessage(message);
      // Small delay to avoid overwhelming the system
      await user1.page.waitForTimeout(100);
    }
    
    // Wait for all messages to be delivered
    await user2.dashboard.waitForNewMessage();
    
    // Verify all messages were delivered
    const finalMessageCount = await user2.dashboard.getMessageCount();
    expect(finalMessageCount).toBeGreaterThanOrEqual(rapidMessages.length);
  });

  test('should handle message delivery across browser refresh', async ({ multiUserPages }) => {
    const { user1, user2 } = multiUserPages;
    
    await TestUtils.setupConversation(user1.dashboard, 'testuser2');
    
    // Send initial message
    await user1.dashboard.sendMessage('Message before refresh');
    await user2.dashboard.waitForNewMessage();
    
    // Refresh user2's browser
    await user2.page.reload();
    await user2.dashboard.waitForPageLoad();
    
    // Send message after refresh
    await user1.dashboard.sendMessage('Message after refresh');
    await user2.dashboard.waitForNewMessage();
    
    // Verify message was delivered after refresh
    const lastMessage = await user2.dashboard.getLastMessage();
    expect(lastMessage).toContain('Message after refresh');
  });
});
