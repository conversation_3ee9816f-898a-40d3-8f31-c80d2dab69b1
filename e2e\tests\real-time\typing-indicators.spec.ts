import { test, expect } from '../../fixtures/test-fixtures';
import { TestUtils } from '../../fixtures/test-fixtures';

test.describe('Typing Indicators', () => {
  test('should show typing indicator when user starts typing', async ({ multiUserPages }) => {
    const { user1, user2 } = multiUserPages;
    
    await TestUtils.setupConversation(user1.dashboard, 'testuser2');
    
    // User1 starts typing
    await user1.dashboard.simulateTyping('Hello...');
    
    // User2 should see typing indicator
    await user2.dashboard.waitForTypingIndicator();
    expect(await user2.dashboard.isTypingIndicatorVisible()).toBe(true);
  });

  test('should hide typing indicator when user stops typing', async ({ multiUserPages }) => {
    const { user1, user2 } = multiUserPages;
    
    await TestUtils.setupConversation(user1.dashboard, 'testuser2');
    
    // User1 starts typing
    await user1.dashboard.simulateTyping('Hello...');
    await user2.dashboard.waitForTypingIndicator();
    
    // User1 stops typing
    await user1.dashboard.stopTyping();
    
    // Typing indicator should disappear
    await user2.dashboard.waitForTypingIndicatorToDisappear();
    expect(await user2.dashboard.isTypingIndicatorVisible()).toBe(false);
  });

  test('should hide typing indicator when message is sent', async ({ multiUserPages }) => {
    const { user1, user2 } = multiUserPages;
    
    await TestUtils.setupConversation(user1.dashboard, 'testuser2');
    
    // User1 starts typing
    await user1.dashboard.simulateTyping('Hello there!');
    await user2.dashboard.waitForTypingIndicator();
    
    // User1 sends the message
    await user1.dashboard.sendMessage('Hello there!');
    
    // Typing indicator should disappear immediately
    await user2.dashboard.waitForTypingIndicatorToDisappear();
    expect(await user2.dashboard.isTypingIndicatorVisible()).toBe(false);
  });

  test('should handle typing indicator timeout', async ({ multiUserPages }) => {
    const { user1, user2 } = multiUserPages;
    
    await TestUtils.setupConversation(user1.dashboard, 'testuser2');
    
    // User1 starts typing
    await user1.dashboard.simulateTyping('Hello...');
    await user2.dashboard.waitForTypingIndicator();
    
    // Wait for typing indicator timeout (usually 3-5 seconds)
    await user1.page.waitForTimeout(6000);
    
    // Typing indicator should disappear due to timeout
    expect(await user2.dashboard.isTypingIndicatorVisible()).toBe(false);
  });

  test('should not show own typing indicator', async ({ multiUserPages }) => {
    const { user1 } = multiUserPages;
    
    await TestUtils.setupConversation(user1.dashboard, 'testuser2');
    
    // User1 starts typing
    await user1.dashboard.simulateTyping('Hello...');
    
    // User1 should not see their own typing indicator
    await user1.page.waitForTimeout(1000);
    expect(await user1.dashboard.isTypingIndicatorVisible()).toBe(false);
  });

  test('should handle multiple users typing simultaneously', async ({ multiUserPages }) => {
    const { user1, user2, user3 } = multiUserPages;
    
    // Set up conversations
    await TestUtils.setupConversation(user1.dashboard, 'testuser2');
    await TestUtils.setupConversation(user2.dashboard, 'testuser1');
    
    // Both users start typing simultaneously
    await Promise.all([
      user1.dashboard.simulateTyping('User 1 typing...'),
      user2.dashboard.simulateTyping('User 2 typing...')
    ]);
    
    // Each user should see the other's typing indicator
    await user1.dashboard.waitForTypingIndicator();
    await user2.dashboard.waitForTypingIndicator();
    
    expect(await user1.dashboard.isTypingIndicatorVisible()).toBe(true);
    expect(await user2.dashboard.isTypingIndicatorVisible()).toBe(true);
  });

  test('should handle typing indicator during network interruption', async ({ multiUserPages }) => {
    const { user1, user2 } = multiUserPages;
    
    await TestUtils.setupConversation(user1.dashboard, 'testuser2');
    
    // User1 starts typing
    await user1.dashboard.simulateTyping('Hello...');
    await user2.dashboard.waitForTypingIndicator();
    
    // Simulate network interruption for user2
    await TestUtils.simulateNetworkIssue(user2.page, 2000);
    
    // Continue typing during interruption
    await user1.dashboard.simulateTyping(' World!');
    
    // After network recovery, typing indicator should work again
    await user2.dashboard.waitForConnectionEstablished();
    
    // Start new typing session
    await user1.dashboard.simulateTyping('New typing session');
    await user2.dashboard.waitForTypingIndicator();
    
    expect(await user2.dashboard.isTypingIndicatorVisible()).toBe(true);
  });

  test('should handle rapid typing start/stop cycles', async ({ multiUserPages }) => {
    const { user1, user2 } = multiUserPages;
    
    await TestUtils.setupConversation(user1.dashboard, 'testuser2');
    
    // Rapid typing cycles
    for (let i = 0; i < 5; i++) {
      await user1.dashboard.simulateTyping(`Cycle ${i + 1}`);
      await user2.dashboard.waitForTypingIndicator();
      
      await user1.dashboard.stopTyping();
      await user2.dashboard.waitForTypingIndicatorToDisappear();
      
      // Small delay between cycles
      await user1.page.waitForTimeout(200);
    }
    
    // Final verification
    expect(await user2.dashboard.isTypingIndicatorVisible()).toBe(false);
  });

  test('should show typing indicator with correct user information', async ({ multiUserPages }) => {
    const { user1, user2 } = multiUserPages;
    
    await TestUtils.setupConversation(user1.dashboard, 'testuser2');
    
    // User1 starts typing
    await user1.dashboard.simulateTyping('Hello...');
    await user2.dashboard.waitForTypingIndicator();
    
    // Verify typing indicator shows correct user information
    const typingIndicator = user2.page.locator('[data-testid="typing-indicator"], .typing-indicator');
    const indicatorText = await typingIndicator.textContent();
    
    // Should contain user1's name or identifier
    expect(indicatorText).toMatch(/typing|testuser1/i);
  });

  test('should handle typing indicator in group conversations', async ({ multiUserPages }) => {
    const { user1, user2, user3 } = multiUserPages;
    
    // This test assumes group conversation functionality exists
    // For now, we'll simulate with multiple direct conversations
    
    await TestUtils.setupConversation(user1.dashboard, 'testuser2');
    await TestUtils.setupConversation(user1.dashboard, 'testuser3');
    
    // User1 starts typing in conversation with user2
    await user1.dashboard.simulateTyping('Group message...');
    
    // User2 should see typing indicator
    await user2.dashboard.waitForTypingIndicator();
    expect(await user2.dashboard.isTypingIndicatorVisible()).toBe(true);
    
    // User3 should also see typing indicator if in same group
    // This would need group conversation implementation
  });

  test('should handle typing indicator persistence across conversation switches', async ({ multiUserPages }) => {
    const { user1, user2 } = multiUserPages;
    
    await TestUtils.setupConversation(user1.dashboard, 'testuser2');
    
    // User1 starts typing
    await user1.dashboard.simulateTyping('Hello...');
    await user2.dashboard.waitForTypingIndicator();
    
    // User2 switches to another conversation (if available)
    // Then switches back
    // This would need multiple conversation implementation
    
    // For now, just verify typing indicator persists during page interactions
    await user2.page.click('body'); // Click somewhere else
    await user2.page.waitForTimeout(500);
    
    // Typing indicator should still be visible
    expect(await user2.dashboard.isTypingIndicatorVisible()).toBe(true);
  });

  test('should handle typing indicator with long text input', async ({ multiUserPages }) => {
    const { user1, user2 } = multiUserPages;
    
    await TestUtils.setupConversation(user1.dashboard, 'testuser2');
    
    // User1 types a very long message
    const longText = 'This is a very long message that simulates a user typing a lot of content. '.repeat(10);
    
    await user1.dashboard.simulateTyping(longText);
    await user2.dashboard.waitForTypingIndicator();
    
    // Typing indicator should remain visible during long typing
    expect(await user2.dashboard.isTypingIndicatorVisible()).toBe(true);
    
    // Send the long message
    await user1.dashboard.sendMessage(longText);
    
    // Typing indicator should disappear
    await user2.dashboard.waitForTypingIndicatorToDisappear();
    expect(await user2.dashboard.isTypingIndicatorVisible()).toBe(false);
  });

  test('should handle typing indicator with special characters', async ({ multiUserPages }) => {
    const { user1, user2 } = multiUserPages;
    
    await TestUtils.setupConversation(user1.dashboard, 'testuser2');
    
    // Type message with special characters
    const specialText = 'Typing with emojis 😀🎉 and symbols !@#$%^&*()';
    
    await user1.dashboard.simulateTyping(specialText);
    await user2.dashboard.waitForTypingIndicator();
    
    expect(await user2.dashboard.isTypingIndicatorVisible()).toBe(true);
    
    // Complete the message
    await user1.dashboard.sendMessage(specialText);
    await user2.dashboard.waitForTypingIndicatorToDisappear();
  });

  test('should handle typing indicator across browser refresh', async ({ multiUserPages }) => {
    const { user1, user2 } = multiUserPages;
    
    await TestUtils.setupConversation(user1.dashboard, 'testuser2');
    
    // User1 starts typing
    await user1.dashboard.simulateTyping('Hello...');
    await user2.dashboard.waitForTypingIndicator();
    
    // User2 refreshes browser
    await user2.page.reload();
    await user2.dashboard.waitForPageLoad();
    
    // Typing indicator should be reset after refresh
    expect(await user2.dashboard.isTypingIndicatorVisible()).toBe(false);
    
    // Start new typing session after refresh
    await user1.dashboard.simulateTyping('After refresh...');
    await user2.dashboard.waitForTypingIndicator();
    
    expect(await user2.dashboard.isTypingIndicatorVisible()).toBe(true);
  });
});
