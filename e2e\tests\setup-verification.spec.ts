import { test, expect } from '@playwright/test';

test.describe('E2E Setup Verification', () => {
  test('should verify <PERSON><PERSON> is working', async ({ page }) => {
    // Simple test to verify Playwright setup
    await page.goto('https://playwright.dev');
    await expect(page).toHaveTitle(/Playwright/);
  });

  test('should verify browser capabilities', async ({ page }) => {
    await page.goto('data:text/html,<h1>Test Page</h1><p>Playwright E2E Setup Test</p>');

    await expect(page.locator('h1')).toHaveText('Test Page');
    await expect(page.locator('p')).toHaveText('Playwright E2E Setup Test');
  });

  test('should verify test fixtures work', async ({ page }) => {
    // Test that we can use page fixture
    expect(page).toBeDefined();

    // Test basic page operations
    await page.setContent('<div id="test">Hello World</div>');
    await expect(page.locator('#test')).toHaveText('Hello World');
  });

  test('should verify multiple browser contexts', async ({ browser }) => {
    const context1 = await browser.newContext();
    const context2 = await browser.newContext();

    const page1 = await context1.newPage();
    const page2 = await context2.newPage();

    await page1.setContent('<div>Context 1</div>');
    await page2.setContent('<div>Context 2</div>');

    await expect(page1.locator('div')).toHaveText('Context 1');
    await expect(page2.locator('div')).toHaveText('Context 2');

    await context1.close();
    await context2.close();
  });

  test.skip('should verify frontend is accessible', async ({ page }) => {
    // Skipped until services are running
    await page.goto('/');

    // Should redirect to login or dashboard
    await page.waitForURL(/\/(login|dashboard)/, { timeout: 10000 });

    // Verify page loads without errors
    expect(page.url()).toMatch(/\/(login|dashboard)/);
  });

  test.skip('should verify backend API is accessible', async ({ request }) => {
    // Skipped until services are running
    try {
      const response = await request.get('http://localhost:8000/api/auth/test/');
      expect([200, 404, 405]).toContain(response.status());
    } catch (error) {
      throw new Error('Backend API is not accessible. Make sure Django server is running on port 8000.');
    }
  });

  test.skip('should verify socket server is accessible', async ({ request }) => {
    // Skipped until services are running
    try {
      const response = await request.get('http://localhost:7000/health');
      expect([200, 404, 405]).toContain(response.status());
    } catch (error) {
      throw new Error('Socket server is not accessible. Make sure Node.js server is running on port 7000.');
    }
  });

  test('should verify page has no console errors', async ({ page }) => {
    const consoleErrors: string[] = [];
    
    page.on('console', msg => {
      if (msg.type() === 'error') {
        consoleErrors.push(msg.text());
      }
    });
    
    await page.goto('/login');
    await page.waitForTimeout(2000); // Wait for any async operations
    
    // Filter out known acceptable errors (adjust as needed)
    const criticalErrors = consoleErrors.filter(error => 
      !error.includes('favicon') && 
      !error.includes('404') &&
      !error.includes('WebSocket')
    );
    
    expect(criticalErrors).toHaveLength(0);
  });

  test('should verify basic navigation works', async ({ page }) => {
    // Start at login
    await page.goto('/login');
    expect(page.url()).toContain('/login');
    
    // Navigate to register
    const registerLink = page.locator('a[href="/register"], a:has-text("register")');
    if (await registerLink.isVisible()) {
      await registerLink.click();
      await page.waitForURL('**/register');
      expect(page.url()).toContain('/register');
      
      // Navigate back to login
      const loginLink = page.locator('a[href="/login"], a:has-text("login")');
      if (await loginLink.isVisible()) {
        await loginLink.click();
        await page.waitForURL('**/login');
        expect(page.url()).toContain('/login');
      }
    }
  });

  test('should verify responsive design', async ({ page }) => {
    await page.goto('/login');
    
    // Test desktop view
    await page.setViewportSize({ width: 1920, height: 1080 });
    await expect(page.locator('input[type="email"]')).toBeVisible();
    
    // Test tablet view
    await page.setViewportSize({ width: 768, height: 1024 });
    await expect(page.locator('input[type="email"]')).toBeVisible();
    
    // Test mobile view
    await page.setViewportSize({ width: 375, height: 667 });
    await expect(page.locator('input[type="email"]')).toBeVisible();
  });
});
