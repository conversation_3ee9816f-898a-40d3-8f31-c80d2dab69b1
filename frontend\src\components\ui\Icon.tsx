// frontend/src/components/ui/Icon.tsx
import React from 'react';
import {
  User,
  Users,
  MessageCircle,
  Send,
  Settings,
  LogOut,
  Eye,
  EyeOff,
  Search,
  Plus,
  X,
  Check,
  AlertCircle,
  AlertTriangle,
  Loader2,
  RefreshCw,
  WifiOff,
  Lock,
  Server
} from 'lucide-react';

// Icon component mapping for consistent usage
export const Icons = {
  user: User,
  users: Users,
  message: MessageCircle,
  'message-circle': MessageCircle,
  send: Send,
  settings: Settings,
  logout: LogOut,
  'log-out': LogOut,
  eye: Eye,
  eyeOff: EyeOff,
  search: Search,
  plus: Plus,
  close: X,
  x: X,
  check: Check,
  alert: AlertCircle,
  'alert-triangle': Alert<PERSON>riangle,
  spinner: Loader2,
  loader: Loader2,
  'refresh-cw': RefreshCw,
  refresh: RefreshCw,
  'wifi-off': WifiOff,
  lock: Lock,
  server: Server,
} as const;

interface IconProps {
  name: keyof typeof Icons;
  size?: number;
  className?: string;
}

export const Icon: React.FC<IconProps> = ({ name, size = 20, className = '' }) => {
  const IconComponent = Icons[name];

  if (!IconComponent) {
    console.error(`Icon "${name}" not found. Available icons:`, Object.keys(Icons));
    // Return a fallback icon (AlertCircle) if the requested icon doesn't exist
    return <AlertCircle size={size} className={`${className} text-red-500`} />;
  }

  return <IconComponent size={size} className={className} />;
};
