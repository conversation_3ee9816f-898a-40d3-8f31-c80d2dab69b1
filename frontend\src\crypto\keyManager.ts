// frontend/src/crypto/keyManager.ts
/**
 * High-level key management utilities for Phase 3 encryption.
 * Handles key generation, bundle creation, and key exchange coordination.
 */

import {
  generateIdentityKeyPair,
  generatePreKeyPair,
  generateSignedPreKey,
  exportKeyPair,
  importKeyPair,
  CryptoError
} from './webCrypto';
import {
  storeIdentityKeys,
  getIdentityKeys,
  storeSignedPreKey,
  getCurrentSignedPreKey,
  storeOneTimePreKeys,
  getUnuploadedOneTimePreKeys,
  markOneTimePreKeyAsUploaded,
  isEncryptionInitialized,
  initializeDatabase
} from './keyStorage';
import type {
  KeyBundleUpload,
  OneTimePreKeyBatch,
  StoredIdentityKeys,
  StoredSignedPreKey,
  StoredOneTimePreKey,
  IdentityKeyPair,
  PreKeyPair,
  EncryptionErrorCode
} from '../types/encryption';

// ============================================================================
// Key Generation Configuration
// ============================================================================

const KEY_GENERATION_CONFIG = {
  // Number of one-time pre-keys to generate in each batch
  ONE_TIME_PREKEY_BATCH_SIZE: 50,
  
  // Minimum number of one-time pre-keys to maintain
  MIN_ONE_TIME_PREKEYS: 10,
  
  // Maximum number of one-time pre-keys to store locally
  MAX_ONE_TIME_PREKEYS: 200,
  
  // Signed pre-key rotation interval (days)
  SIGNED_PREKEY_ROTATION_DAYS: 7
} as const;

// ============================================================================
// Initialization
// ============================================================================

/**
 * Initialize encryption for the current user
 */
export async function initializeEncryption(
  onProgress?: (progress: number, message: string) => void
): Promise<void> {
  try {
    // Initialize database
    onProgress?.(10, 'Initializing secure storage...');
    await initializeDatabase();
    
    // Check if already initialized
    if (await isEncryptionInitialized()) {
      onProgress?.(100, 'Encryption already initialized');
      return;
    }
    
    // Generate identity key pair
    onProgress?.(20, 'Generating identity keys...');
    const identityKeyPair = await generateIdentityKeyPair();
    const serializedIdentityKeys = await exportKeyPair(identityKeyPair);
    
    const identityKeys: StoredIdentityKeys = {
      publicKey: serializedIdentityKeys.publicKey,
      privateKey: serializedIdentityKeys.privateKey,
      createdAt: new Date().toISOString()
    };
    
    await storeIdentityKeys(identityKeys);
    
    // Generate signed pre-key
    onProgress?.(40, 'Generating signed pre-key...');
    await generateAndStoreSignedPreKey(identityKeyPair.privateKey);
    
    // Generate initial batch of one-time pre-keys
    onProgress?.(60, 'Generating one-time pre-keys...');
    await generateOneTimePreKeyBatch(KEY_GENERATION_CONFIG.ONE_TIME_PREKEY_BATCH_SIZE);
    
    onProgress?.(100, 'Encryption initialized successfully');
    
  } catch (error) {
    throw new CryptoError(
      'KEY_GENERATION_FAILED',
      'Failed to initialize encryption',
      { error: error instanceof Error ? error.message : String(error) }
    );
  }
}

/**
 * Check if encryption needs to be initialized
 */
export async function needsInitialization(): Promise<boolean> {
  try {
    return !(await isEncryptionInitialized());
  } catch (error) {
    console.error('Error checking initialization status:', error);
    return true;
  }
}

// ============================================================================
// Key Bundle Generation
// ============================================================================

/**
 * Generate a key bundle for upload to the server
 */
export async function generateKeyBundle(): Promise<KeyBundleUpload> {
  try {
    // Get identity keys
    const identityKeys = await getIdentityKeys();
    if (!identityKeys) {
      throw new CryptoError(
        'MISSING_KEY_BUNDLE',
        'Identity keys not found. Please initialize encryption first.'
      );
    }
    
    // Get current signed pre-key
    const signedPreKey = await getCurrentSignedPreKey();
    if (!signedPreKey) {
      throw new CryptoError(
        'MISSING_KEY_BUNDLE',
        'Signed pre-key not found. Please initialize encryption first.'
      );
    }
    
    return {
      identity_public_key: identityKeys.publicKey,
      signed_prekey_id: signedPreKey.id,
      signed_prekey_public: signedPreKey.publicKey,
      signed_prekey_signature: signedPreKey.signature
    };
    
  } catch (error) {
    if (error instanceof CryptoError) {
      throw error;
    }
    throw new CryptoError(
      'KEY_GENERATION_FAILED',
      'Failed to generate key bundle',
      { error: error instanceof Error ? error.message : String(error) }
    );
  }
}

/**
 * Generate one-time pre-key batch for upload
 */
export async function generateOneTimePreKeyBatch(count: number = KEY_GENERATION_CONFIG.ONE_TIME_PREKEY_BATCH_SIZE): Promise<OneTimePreKeyBatch> {
  try {
    const preKeys: StoredOneTimePreKey[] = [];
    const uploadBatch: OneTimePreKeyBatch = { prekeys: [] };
    
    // Get the highest existing pre-key ID to avoid conflicts
    const existingPreKeys = await getUnuploadedOneTimePreKeys();
    let nextId = existingPreKeys.length > 0 
      ? Math.max(...existingPreKeys.map(pk => pk.id)) + 1 
      : 1;
    
    for (let i = 0; i < count; i++) {
      // Generate key pair
      const keyPair = await generatePreKeyPair();
      const serializedKeys = await exportKeyPair(keyPair);
      
      const preKey: StoredOneTimePreKey = {
        id: nextId,
        publicKey: serializedKeys.publicKey,
        privateKey: serializedKeys.privateKey,
        uploaded: false,
        used: false,
        createdAt: new Date().toISOString()
      };
      
      preKeys.push(preKey);
      uploadBatch.prekeys.push({
        key_id: nextId,
        public_key: serializedKeys.publicKey
      });
      
      nextId++;
    }
    
    // Store all pre-keys locally
    await storeOneTimePreKeys(preKeys);
    
    return uploadBatch;
    
  } catch (error) {
    throw new CryptoError(
      'KEY_GENERATION_FAILED',
      'Failed to generate one-time pre-key batch',
      { error: error instanceof Error ? error.message : String(error) }
    );
  }
}

/**
 * Get unuploaded one-time pre-keys for upload
 */
export async function getUnuploadedPreKeyBatch(): Promise<OneTimePreKeyBatch | null> {
  try {
    const unuploadedKeys = await getUnuploadedOneTimePreKeys();
    
    if (unuploadedKeys.length === 0) {
      return null;
    }
    
    return {
      prekeys: unuploadedKeys.map(preKey => ({
        key_id: preKey.id,
        public_key: preKey.publicKey
      }))
    };
    
  } catch (error) {
    throw new CryptoError(
      'STORAGE_FAILED',
      'Failed to get unuploaded pre-keys',
      { error: error instanceof Error ? error.message : String(error) }
    );
  }
}

/**
 * Mark pre-keys as uploaded after successful upload
 */
export async function markPreKeysAsUploaded(keyIds: number[]): Promise<void> {
  try {
    for (const keyId of keyIds) {
      await markOneTimePreKeyAsUploaded(keyId);
    }
  } catch (error) {
    throw new CryptoError(
      'STORAGE_FAILED',
      'Failed to mark pre-keys as uploaded',
      { error: error instanceof Error ? error.message : String(error) }
    );
  }
}

// ============================================================================
// Key Rotation and Maintenance
// ============================================================================

/**
 * Generate and store a new signed pre-key
 */
async function generateAndStoreSignedPreKey(identityPrivateKey: CryptoKey): Promise<StoredSignedPreKey> {
  // Get next pre-key ID
  const currentPreKey = await getCurrentSignedPreKey();
  const nextId = currentPreKey ? currentPreKey.id + 1 : 1;
  
  // Generate signed pre-key
  const { keyPair, signature, publicKeySpki } = await generateSignedPreKey(identityPrivateKey, nextId);
  const serializedKeys = await exportKeyPair(keyPair);
  
  const signedPreKey: StoredSignedPreKey = {
    id: nextId,
    publicKey: publicKeySpki,
    privateKey: serializedKeys.privateKey,
    signature,
    createdAt: new Date().toISOString()
  };
  
  await storeSignedPreKey(signedPreKey);
  return signedPreKey;
}

/**
 * Check if signed pre-key needs rotation
 */
export async function needsSignedPreKeyRotation(): Promise<boolean> {
  try {
    const currentPreKey = await getCurrentSignedPreKey();
    if (!currentPreKey) return true;
    
    const createdAt = new Date(currentPreKey.createdAt);
    const now = new Date();
    const daysSinceCreation = (now.getTime() - createdAt.getTime()) / (1000 * 60 * 60 * 24);
    
    return daysSinceCreation >= KEY_GENERATION_CONFIG.SIGNED_PREKEY_ROTATION_DAYS;
  } catch (error) {
    console.error('Error checking signed pre-key rotation:', error);
    return true;
  }
}

/**
 * Rotate signed pre-key if needed
 */
export async function rotateSignedPreKeyIfNeeded(): Promise<boolean> {
  try {
    if (!(await needsSignedPreKeyRotation())) {
      return false;
    }
    
    // Get identity private key
    const identityKeys = await getIdentityKeys();
    if (!identityKeys) {
      throw new CryptoError('MISSING_KEY_BUNDLE', 'Identity keys not found');
    }
    
    const identityKeyPair = await importKeyPair(identityKeys, 'ECDSA');
    await generateAndStoreSignedPreKey(identityKeyPair.privateKey);
    
    return true;
  } catch (error) {
    throw new CryptoError(
      'KEY_GENERATION_FAILED',
      'Failed to rotate signed pre-key',
      { error: error instanceof Error ? error.message : String(error) }
    );
  }
}

/**
 * Check if more one-time pre-keys need to be generated
 */
export async function needsMoreOneTimePreKeys(): Promise<boolean> {
  try {
    const unuploadedKeys = await getUnuploadedOneTimePreKeys();
    return unuploadedKeys.length < KEY_GENERATION_CONFIG.MIN_ONE_TIME_PREKEYS;
  } catch (error) {
    console.error('Error checking one-time pre-key count:', error);
    return true;
  }
}

/**
 * Generate more one-time pre-keys if needed
 */
export async function generateMoreOneTimePreKeysIfNeeded(): Promise<OneTimePreKeyBatch | null> {
  try {
    if (!(await needsMoreOneTimePreKeys())) {
      return null;
    }
    
    return await generateOneTimePreKeyBatch();
  } catch (error) {
    throw new CryptoError(
      'KEY_GENERATION_FAILED',
      'Failed to generate more one-time pre-keys',
      { error: error instanceof Error ? error.message : String(error) }
    );
  }
}
