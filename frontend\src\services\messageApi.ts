// frontend/src/services/messageApi.ts
import { api } from './api';
import type { ApiResponse } from '../types';

// Direct pagination response from Django REST Framework (not wrapped in ApiResponse)
export interface MessagesPaginatedResponse {
  count: number;
  next?: string;
  previous?: string;
  results: Message[];
}

export interface Message {
  id: string;
  conversation_id: string; // Backend uses snake_case
  conversationId: string; // Frontend normalized field
  sender: {
    id: string;
    username: string;
    first_name?: string;
    last_name?: string;
    profile_picture?: string;
  };
  content: string;
  message_type: 'TEXT' | 'IMAGE' | 'FILE' | 'SYSTEM'; // Backend uses snake_case
  messageType: 'TEXT' | 'IMAGE' | 'FILE' | 'SYSTEM'; // Frontend normalized field
  created_at: string; // Backend uses snake_case
  createdAt: string; // Frontend normalized field
  updated_at: string; // Backend uses snake_case
  updatedAt: string; // Frontend normalized field
  status?: 'SENT' | 'DELIVERED' | 'READ' | 'FAILED'; // Message status from backend
}

export interface SendMessageRequest {
  conversationId: string;
  content: string;
  messageType?: 'TEXT' | 'IMAGE' | 'FILE' | 'SYSTEM';
}

export interface FetchMessagesRequest {
  conversationId: string;
  page?: number;
}

export const messageApi = api.injectEndpoints({
  endpoints: (builder) => ({
    getMessages: builder.query<MessagesPaginatedResponse, FetchMessagesRequest>({
      query: ({ conversationId, page = 1 }) => ({
        url: `/messaging/conversations/${conversationId}/messages/`,
        params: { page },
      }),
      providesTags: (result, error, { conversationId }) => [
        { type: 'Message', id: conversationId },
        { type: 'Message', id: 'LIST' },
      ],
      serializeQueryArgs: ({ queryArgs }) => {
        const { conversationId } = queryArgs;
        return conversationId;
      },
      merge: (currentCache, newItems, { arg }) => {
        const { page = 1 } = arg;

        if (page === 1 || !currentCache) {
          // Replace cache for first page or if no existing cache
          return newItems;
        } else {
          // Merge for pagination - prepend older messages
          // Ensure we don't add duplicates
          const existingIds = new Set(currentCache.results.map(msg => msg.id));
          const newMessages = newItems.results.filter(msg => !existingIds.has(msg.id));

          return {
            ...newItems,
            results: [
              ...newMessages,
              ...currentCache.results,
            ],
            count: Math.max(newItems.count, currentCache.count),
          };
        }
      },
      forceRefetch({ currentArg, previousArg }) {
        return currentArg?.conversationId !== previousArg?.conversationId;
      },
      transformErrorResponse: (response: any) => ({
        status: response.status,
        data: response.data || {
          success: false,
          error: 'Failed to fetch messages',
          timestamp: new Date().toISOString()
        }
      }),
      // Transform response to normalize field names from snake_case to camelCase
      transformResponse: (response: any): MessagesPaginatedResponse => {
        if (!response || !response.results) {
          return response;
        }
        return {
          ...response,
          results: response.results.map((message: any) => ({
            ...message,
            conversationId: message.conversation_id,
            messageType: message.message_type,
            createdAt: message.created_at,
            updatedAt: message.updated_at,
            // Preserve status field from backend
            status: message.status,
          }))
        };
      },
      // Populate Redux store with message status data after successful fetch
      onQueryStarted: async ({ conversationId }, { dispatch, queryFulfilled }) => {
        try {
          const result = await queryFulfilled;
          if (result.data && result.data.results) {
            // Import the action here to avoid circular dependency
            const { updateMessageStatus } = await import('../store/slices/messageSlice');

            // Populate message statuses in Redux store
            result.data.results.forEach((message: any) => {
              if (message.status) {
                dispatch(updateMessageStatus({
                  messageId: message.id,
                  status: message.status
                }));
              }
            });
          }
        } catch (error) {
          console.error('Failed to populate message statuses:', error);
        }
      },
    }),

    sendMessage: builder.mutation<ApiResponse<Message>, SendMessageRequest>({
      query: ({ conversationId, content, messageType = 'TEXT' }) => ({
        url: `/messaging/conversations/${conversationId}/send/`,
        method: 'POST',
        body: { content, messageType },
      }),
      invalidatesTags: (result, error, { conversationId }) => [
        { type: 'Message', id: conversationId },
        { type: 'Conversation', id: conversationId },
        { type: 'Conversation', id: 'LIST' },
      ],
      // Optimistic update
      onQueryStarted: async ({ conversationId, content, messageType = 'TEXT' }, { dispatch, queryFulfilled, getState }) => {
        // Get current user data from localStorage or state
        let currentUser = {
          id: 'current-user',
          username: 'You',
          first_name: '',
          last_name: '',
        };

        try {
          const userData = localStorage.getItem('userData');
          if (userData) {
            const parsedUser = JSON.parse(userData);
            currentUser = {
              id: parsedUser.id || 'current-user',
              username: parsedUser.username || 'You',
              first_name: parsedUser.firstName || '',
              last_name: parsedUser.lastName || '',
            };
          }
        } catch (error) {
          console.warn('Failed to parse user data from localStorage:', error);
        }

        // Create optimistic message
        const tempId = `temp-${Date.now()}-${Math.random()}`;
        const timestamp = new Date().toISOString();
        const optimisticMessage: Message = {
          id: tempId,
          conversation_id: conversationId,
          conversationId,
          sender: currentUser,
          content,
          message_type: messageType as Message['message_type'],
          messageType: messageType as Message['messageType'],
          created_at: timestamp,
          createdAt: timestamp,
          updated_at: timestamp,
          updatedAt: timestamp,
        };

        // Optimistically update the cache
        const patchResult = dispatch(
          messageApi.util.updateQueryData('getMessages', { conversationId }, (draft) => {
            if (draft && draft.results) {
              draft.results.push(optimisticMessage);
            }
          })
        );

        try {
          const result = await queryFulfilled;
          
          // Replace optimistic message with real message
          dispatch(
            messageApi.util.updateQueryData('getMessages', { conversationId }, (draft) => {
              if (draft.data && draft.data.results) {
                const index = draft.data.results.findIndex(msg => msg.id === tempId);
                if (index !== -1 && result.data.success && result.data.data) {
                  draft.data.results[index] = result.data.data;
                }
              }
            })
          );
        } catch (error) {
          // Revert optimistic update on error
          patchResult.undo();
        }
      },
      transformErrorResponse: (response: any) => ({
        status: response.status,
        data: response.data || { 
          success: false, 
          error: 'Failed to send message', 
          timestamp: new Date().toISOString() 
        }
      }),
    }),

    // For real-time message updates from socket
    addMessageToCache: builder.mutation<void, { conversationId: string; message: Message; tempId?: string }>({
      queryFn: () => ({ data: undefined }),
      onQueryStarted: async ({ conversationId, message, tempId }, { dispatch }) => {
        console.log('🔶 [RTK_CACHE] Adding message to cache');
        console.log('🔶 [RTK_CACHE] Message ID:', message.id);
        console.log('🔶 [RTK_CACHE] TempId:', tempId);
        console.log('🔶 [RTK_CACHE] Conversation:', conversationId);
        console.log('🔶 [RTK_CACHE] Content:', message.content);

        // Add message to cache without API call
        dispatch(
          messageApi.util.updateQueryData('getMessages', { conversationId }, (draft) => {
            if (draft && draft.results) {
              console.log('🔶 [RTK_CACHE] Current cache has', draft.results.length, 'messages');
              console.log('🔶 [RTK_CACHE] Message isOptimistic:', message.isOptimistic);

              // If tempId is provided and this is NOT an optimistic message, try to replace optimistic message first
              if (tempId && !message.isOptimistic) {
                console.log('🔶 [RTK_CACHE] Looking for optimistic message to replace with tempId:', tempId);
                const optimisticIndex = draft.results.findIndex(msg => msg.id === tempId);
                console.log('🔶 [RTK_CACHE] Optimistic message index:', optimisticIndex);

                if (optimisticIndex !== -1) {
                  console.log('🔶 [RTK_CACHE] ✅ Replacing optimistic message at index', optimisticIndex);
                  // Replace optimistic message with real message
                  draft.results[optimisticIndex] = message;
                  return;
                }
              }

              // Additional fallback: look for optimistic message by content and sender
              // This handles the case where new_message arrives before message_sent
              console.log('🔶 [RTK_CACHE] Checking content-based fallback conditions...');
              console.log('🔶 [RTK_CACHE] !tempId:', !tempId);
              console.log('🔶 [RTK_CACHE] message.sender:', message.sender);
              console.log('🔶 [RTK_CACHE] message.isOptimistic:', message.isOptimistic);

              if (!tempId && message.sender && !message.isOptimistic) {
                console.log('🔶 [RTK_CACHE] Looking for optimistic message by content...');
                console.log('🔶 [RTK_CACHE] Searching for content:', message.content);
                console.log('🔶 [RTK_CACHE] Searching for sender:', message.sender.id);

                // Debug: log all messages in cache
                draft.results.forEach((msg, index) => {
                  console.log(`🔶 [RTK_CACHE] Cache[${index}]: ID=${msg.id}, content="${msg.content}", isOptimistic=${msg.isOptimistic}, sender=${msg.sender?.id}`);
                });

                const optimisticByContentIndex = draft.results.findIndex(msg =>
                  msg.isOptimistic &&
                  msg.content === message.content &&
                  msg.sender.id === message.sender.id &&
                  Math.abs(
                    new Date(msg.createdAt || msg.created_at).getTime() -
                    new Date(message.createdAt || message.created_at).getTime()
                  ) < 10000 // Within 10 seconds
                );
                console.log('🔶 [RTK_CACHE] Optimistic by content index:', optimisticByContentIndex);

                if (optimisticByContentIndex !== -1) {
                  console.log('🔶 [RTK_CACHE] ✅ Replacing optimistic message by content at index', optimisticByContentIndex);
                  // Replace optimistic message with real message
                  draft.results[optimisticByContentIndex] = message;
                  return;
                }
              }

              // Check if message already exists by real ID
              console.log('🔶 [RTK_CACHE] Checking for existing message with ID:', message.id);
              const existsByIdIndex = draft.results.findIndex(msg => msg.id === message.id);
              console.log('🔶 [RTK_CACHE] Existing message index:', existsByIdIndex);

              if (existsByIdIndex !== -1) {
                console.log('🔶 [RTK_CACHE] ⚠️ Message already exists, skipping duplicate');
                // Message already exists, don't add duplicate
                return;
              }

              // AGGRESSIVE DUPLICATE PREVENTION: Check for messages with same content and sender
              // This prevents duplicates even if IDs are different
              console.log('🔶 [RTK_CACHE] Checking for content duplicates...');
              const contentDuplicateIndex = draft.results.findIndex(msg =>
                msg.content === message.content &&
                msg.sender.id === message.sender.id &&
                Math.abs(
                  new Date(msg.createdAt || msg.created_at).getTime() -
                  new Date(message.createdAt || message.created_at).getTime()
                ) < 30000 // Within 30 seconds
              );
              console.log('🔶 [RTK_CACHE] Content duplicate index:', contentDuplicateIndex);

              if (contentDuplicateIndex !== -1) {
                console.log('🔶 [RTK_CACHE] ⚠️ Content duplicate found, replacing instead of adding');
                // Replace the existing message with the new one (it might have better data)
                draft.results[contentDuplicateIndex] = message;
                return;
              }

              // Fallback: Check for potential duplicates by content and sender (for offline scenarios)
              console.log('🔶 [RTK_CACHE] Checking for potential duplicates by content...');
              const potentialDuplicateIndex = draft.results.findIndex(msg =>
                msg.content === message.content &&
                msg.sender.id === message.sender.id &&
                Math.abs(
                  new Date(msg.createdAt || msg.created_at).getTime() -
                  new Date(message.createdAt || message.created_at).getTime()
                ) < 5000 // Within 5 seconds
              );
              console.log('🔶 [RTK_CACHE] Potential duplicate index:', potentialDuplicateIndex);

              if (potentialDuplicateIndex !== -1) {
                console.log('🔶 [RTK_CACHE] ✅ Replacing potential duplicate at index', potentialDuplicateIndex);
                // Replace potential duplicate with the real message (it likely has a real ID)
                draft.results[potentialDuplicateIndex] = message;
              } else {
                console.log('🔶 [RTK_CACHE] ✅ Adding new message to cache');
                // Add new message
                draft.results.push(message);
              }

              console.log('🔶 [RTK_CACHE] Sorting messages by creation date...');
              // Sort messages by creation date
              draft.results.sort((a, b) =>
                new Date(a.createdAt || a.created_at).getTime() -
                new Date(b.createdAt || b.created_at).getTime()
              );
              console.log('🔶 [RTK_CACHE] ✅ Cache now has', draft.results.length, 'messages');
            }
          })
        );
      },
    }),

    // Remove optimistic message from cache (for cleanup)
    removeOptimisticMessageFromCache: builder.mutation<void, { conversationId: string; tempId: string }>({
      queryFn: () => ({ data: undefined }),
      onQueryStarted: async ({ conversationId, tempId }, { dispatch }) => {
        dispatch(
          messageApi.util.updateQueryData('getMessages', { conversationId }, (draft) => {
            if (draft && draft.results) {
              const index = draft.results.findIndex(msg => msg.id === tempId);
              if (index !== -1) {
                draft.results.splice(index, 1);
              }
            }
          })
        );
      },
    }),
  }),
  overrideExisting: false,
});

export const {
  useGetMessagesQuery,
  useLazyGetMessagesQuery,
  useSendMessageMutation,
  useAddMessageToCacheMutation,
  useRemoveOptimisticMessageFromCacheMutation,
} = messageApi;
