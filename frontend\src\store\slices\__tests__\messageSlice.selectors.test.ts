// frontend/src/store/slices/__tests__/messageSlice.selectors.test.ts
import {
  selectMessagesByConversation,
  selectSortedMessagesByConversation,
  selectTypingUsersByConversation,
  selectSendingMessages,
  selectMessageStatuses,
  selectFailedMessages
} from '../messageSlice';
import type { MessageState } from '../messageSlice';

describe('Message Selectors Memoization', () => {
  const mockState = {
    messages: {
      messages: {
        'conv-1': [
          {
            id: '1',
            conversationId: 'conv-1',
            sender: { id: 'user1', username: 'user1' },
            content: 'Hello',
            messageType: 'TEXT' as const,
            createdAt: '2023-01-01T10:00:00Z',
            updatedAt: '2023-01-01T10:00:00Z'
          },
          {
            id: '2',
            conversationId: 'conv-1',
            sender: { id: 'user2', username: 'user2' },
            content: 'Hi there',
            messageType: 'TEXT' as const,
            createdAt: '2023-01-01T09:00:00Z',
            updatedAt: '2023-01-01T09:00:00Z'
          }
        ]
      },
      sendingMessages: { 'temp-1': true },
      messageStatuses: { '1': 'READ' as const },
      failedMessages: { '2': true },
      typingUsers: { 'conv-1': ['user3'] },
      loading: false,
      error: null,
      optimisticMessageMap: {},
    } as MessageState
  };

  describe('selectMessagesByConversation', () => {
    it('should return messages for existing conversation', () => {
      const result = selectMessagesByConversation(mockState, 'conv-1');
      expect(result).toHaveLength(2);
      expect(result[0].id).toBe('1');
    });

    it('should return empty array for non-existing conversation', () => {
      const result = selectMessagesByConversation(mockState, 'conv-999');
      expect(result).toEqual([]);
    });

    it('should return the same reference when called multiple times with same data', () => {
      const result1 = selectMessagesByConversation(mockState, 'conv-1');
      const result2 = selectMessagesByConversation(mockState, 'conv-1');
      expect(result1).toBe(result2); // Same reference
    });

    it('should return the same empty array reference for non-existing conversations', () => {
      const result1 = selectMessagesByConversation(mockState, 'conv-999');
      const result2 = selectMessagesByConversation(mockState, 'conv-888');
      expect(result1).toBe(result2); // Same empty array reference
    });
  });

  describe('selectSortedMessagesByConversation', () => {
    it('should return messages sorted by creation time', () => {
      const result = selectSortedMessagesByConversation(mockState, 'conv-1');
      expect(result).toHaveLength(2);
      // Should be sorted by createdAt (earliest first)
      expect(result[0].id).toBe('2'); // 09:00:00
      expect(result[1].id).toBe('1'); // 10:00:00
    });

    it('should return the same reference when called multiple times with same data', () => {
      const result1 = selectSortedMessagesByConversation(mockState, 'conv-1');
      const result2 = selectSortedMessagesByConversation(mockState, 'conv-1');
      expect(result1).toBe(result2); // Same reference due to memoization
    });

    it('should return empty array for non-existing conversation', () => {
      const result = selectSortedMessagesByConversation(mockState, 'conv-999');
      expect(result).toEqual([]);
    });
  });

  describe('selectTypingUsersByConversation', () => {
    it('should return typing users for existing conversation', () => {
      const result = selectTypingUsersByConversation(mockState, 'conv-1');
      expect(result).toEqual(['user3']);
    });

    it('should return empty array for conversation with no typing users', () => {
      const result = selectTypingUsersByConversation(mockState, 'conv-999');
      expect(result).toEqual([]);
    });

    it('should return the same reference when called multiple times', () => {
      const result1 = selectTypingUsersByConversation(mockState, 'conv-1');
      const result2 = selectTypingUsersByConversation(mockState, 'conv-1');
      expect(result1).toBe(result2);
    });
  });

  describe('Other selectors memoization', () => {
    it('should return the same reference for sendingMessages', () => {
      const result1 = selectSendingMessages(mockState);
      const result2 = selectSendingMessages(mockState);
      expect(result1).toBe(result2);
      expect(result1).toEqual({ 'temp-1': true });
    });

    it('should return the same reference for messageStatuses', () => {
      const result1 = selectMessageStatuses(mockState);
      const result2 = selectMessageStatuses(mockState);
      expect(result1).toBe(result2);
      expect(result1).toEqual({ '1': 'READ' });
    });

    it('should return the same reference for failedMessages', () => {
      const result1 = selectFailedMessages(mockState);
      const result2 = selectFailedMessages(mockState);
      expect(result1).toBe(result2);
      expect(result1).toEqual({ '2': true });
    });
  });

  describe('Memoization with state changes', () => {
    it('should return new reference when messages change', () => {
      const result1 = selectMessagesByConversation(mockState, 'conv-1');
      
      const newState = {
        ...mockState,
        messages: {
          ...mockState.messages,
          messages: {
            ...mockState.messages.messages,
            'conv-1': [
              ...mockState.messages.messages['conv-1'],
              {
                id: '3',
                conversationId: 'conv-1',
                sender: { id: 'user1', username: 'user1' },
                content: 'New message',
                messageType: 'TEXT' as const,
                createdAt: '2023-01-01T11:00:00Z',
                updatedAt: '2023-01-01T11:00:00Z'
              }
            ]
          }
        }
      };
      
      const result2 = selectMessagesByConversation(newState, 'conv-1');
      expect(result1).not.toBe(result2); // Different reference
      expect(result2).toHaveLength(3);
    });
  });
});
