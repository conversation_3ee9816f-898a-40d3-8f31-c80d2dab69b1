# Phase 4: Group Chat Functionality

**Duration**: 2-3 weeks | **Priority**: High

## Overview
This phase extends the messaging system to support group conversations with multiple participants, including group management, member permissions, and group-specific encryption handling.

## Prerequisites
- Phase 3 completed successfully
- End-to-end encryption working for direct messages
- Core messaging infrastructure stable
- User authentication and authorization working

## Group Chat Architecture

### Group Types
- **Small Groups**: Up to 50 members, full encryption
- **Large Groups**: Up to 500 members, server-side key management
- **Public Groups**: Open membership, optional encryption

### Permission System
- **Admin**: Full group management permissions
- **Moderator**: Can manage messages and members
- **Member**: Can send messages and view content
- **Restricted**: Read-only access

## Database Schema Updates

### Step 1: Extend Conversation Model

```python
# backend/apps/messaging/models.py - Update existing models

class Conversation(models.Model):
    # ... existing fields ...
    
    # Group-specific fields
    max_participants = models.IntegerField(default=50)
    is_public = models.BooleanField(default=False)
    invite_link = models.CharField(max_length=100, unique=True, null=True, blank=True)
    group_settings = models.JSONField(default=dict)  # Store group preferences
    
    # Group metadata
    avatar_url = models.URLField(blank=True, null=True)
    pinned_message = models.ForeignKey('Message', on_delete=models.SET_NULL, null=True, blank=True, related_name='pinned_in_conversations')
    
    def save(self, *args, **kwargs):
        if self.type == 'group' and not self.invite_link:
            self.invite_link = self.generate_invite_link()
        super().save(*args, **kwargs)
    
    def generate_invite_link(self):
        import secrets
        return secrets.token_urlsafe(16)

class ConversationParticipant(models.Model):
    # ... existing fields ...
    
    # Enhanced permissions
    PARTICIPANT_ROLES = [
        ('admin', 'Admin'),
        ('moderator', 'Moderator'),
        ('member', 'Member'),
        ('restricted', 'Restricted'),
    ]
    
    role = models.CharField(max_length=15, choices=PARTICIPANT_ROLES, default='member')
    
    # Group-specific permissions
    can_add_members = models.BooleanField(default=False)
    can_remove_members = models.BooleanField(default=False)
    can_edit_group_info = models.BooleanField(default=False)
    can_pin_messages = models.BooleanField(default=False)
    can_delete_messages = models.BooleanField(default=False)
    
    # Notification preferences
    notifications_enabled = models.BooleanField(default=True)
    mention_notifications_only = models.BooleanField(default=False)
    
    def save(self, *args, **kwargs):
        # Set permissions based on role
        if self.role == 'admin':
            self.can_add_members = True
            self.can_remove_members = True
            self.can_edit_group_info = True
            self.can_pin_messages = True
            self.can_delete_messages = True
        elif self.role == 'moderator':
            self.can_pin_messages = True
            self.can_delete_messages = True
        
        super().save(*args, **kwargs)

class GroupInvite(models.Model):
    """Track group invitations"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    conversation = models.ForeignKey(Conversation, on_delete=models.CASCADE, related_name='invites')
    invited_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='sent_invites')
    invited_user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='received_invites', null=True, blank=True)
    invited_email = models.EmailField(null=True, blank=True)  # For non-users
    invite_code = models.CharField(max_length=50, unique=True)
    expires_at = models.DateTimeField()
    is_used = models.BooleanField(default=False)
    used_at = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        db_table = 'group_invites'
        unique_together = ['conversation', 'invited_user']

class GroupEvent(models.Model):
    """Track group events for audit and notifications"""
    EVENT_TYPES = [
        ('member_added', 'Member Added'),
        ('member_removed', 'Member Removed'),
        ('member_left', 'Member Left'),
        ('role_changed', 'Role Changed'),
        ('group_info_updated', 'Group Info Updated'),
        ('message_pinned', 'Message Pinned'),
        ('message_unpinned', 'Message Unpinned'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    conversation = models.ForeignKey(Conversation, on_delete=models.CASCADE, related_name='events')
    event_type = models.CharField(max_length=20, choices=EVENT_TYPES)
    actor = models.ForeignKey(User, on_delete=models.CASCADE, related_name='group_actions')
    target_user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='group_events_received', null=True, blank=True)
    event_data = models.JSONField(default=dict)  # Additional event details
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        db_table = 'group_events'
        ordering = ['-created_at']
```

### Step 2: Group Encryption Model

```python
# backend/apps/encryption/models.py - Add group encryption support

class GroupSession(models.Model):
    """Manages encryption for group conversations"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    conversation = models.OneToOneField('messaging.Conversation', on_delete=models.CASCADE, related_name='group_session')
    session_id = models.CharField(max_length=100, unique=True)
    current_epoch = models.IntegerField(default=0)  # For key rotation
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'group_sessions'

class GroupMemberKey(models.Model):
    """Individual member keys for group encryption"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    group_session = models.ForeignKey(GroupSession, on_delete=models.CASCADE, related_name='member_keys')
    member = models.ForeignKey(User, on_delete=models.CASCADE, related_name='group_member_keys')
    encrypted_group_key = models.TextField()  # Group key encrypted with member's public key
    epoch = models.IntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        db_table = 'group_member_keys'
        unique_together = ['group_session', 'member', 'epoch']

class GroupKeyRotation(models.Model):
    """Track group key rotations"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    group_session = models.ForeignKey(GroupSession, on_delete=models.CASCADE, related_name='key_rotations')
    old_epoch = models.IntegerField()
    new_epoch = models.IntegerField()
    rotated_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='initiated_rotations')
    reason = models.CharField(max_length=50)  # 'member_added', 'member_removed', 'scheduled'
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        db_table = 'group_key_rotations'
```

## API Implementation

### Step 3: Group Management APIs

```python
# backend/apps/messaging/views.py - Add group-specific views

from django.db import transaction
from .models import GroupInvite, GroupEvent

@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def create_group(request):
    """Create a new group conversation"""
    serializer = CreateGroupSerializer(data=request.data)
    if serializer.is_valid():
        data = serializer.validated_data
        
        with transaction.atomic():
            # Create group conversation
            group = Conversation.objects.create(
                type='group',
                name=data['name'],
                description=data.get('description', ''),
                created_by=request.user,
                max_participants=data.get('max_participants', 50),
                is_public=data.get('is_public', False)
            )
            
            # Add creator as admin
            ConversationParticipant.objects.create(
                conversation=group,
                user=request.user,
                role='admin'
            )
            
            # Add initial members
            for user_id in data.get('member_ids', []):
                if user_id != request.user.id:
                    ConversationParticipant.objects.create(
                        conversation=group,
                        user_id=user_id,
                        role='member'
                    )
                    
                    # Create group event
                    GroupEvent.objects.create(
                        conversation=group,
                        event_type='member_added',
                        actor=request.user,
                        target_user_id=user_id
                    )
            
            # Initialize group encryption
            from apps.encryption.utils import initialize_group_encryption
            initialize_group_encryption(group)
        
        return Response(
            ConversationSerializer(group, context={'request': request}).data,
            status=status.HTTP_201_CREATED
        )
    
    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def add_group_member(request, conversation_id):
    """Add a member to a group"""
    group = get_object_or_404(Conversation, id=conversation_id, type='group')
    
    # Check permissions
    participant = group.participants.filter(user=request.user, is_active=True).first()
    if not participant or not participant.can_add_members:
        return Response({'error': 'Permission denied'}, status=status.HTTP_403_FORBIDDEN)
    
    user_id = request.data.get('user_id')
    role = request.data.get('role', 'member')
    
    try:
        user_to_add = User.objects.get(id=user_id)
        
        # Check if already a member
        if group.participants.filter(user=user_to_add, is_active=True).exists():
            return Response({'error': 'User is already a member'}, status=status.HTTP_400_BAD_REQUEST)
        
        # Check group capacity
        current_members = group.participants.filter(is_active=True).count()
        if current_members >= group.max_participants:
            return Response({'error': 'Group is at maximum capacity'}, status=status.HTTP_400_BAD_REQUEST)
        
        with transaction.atomic():
            # Add member
            new_participant = ConversationParticipant.objects.create(
                conversation=group,
                user=user_to_add,
                role=role
            )
            
            # Create group event
            GroupEvent.objects.create(
                conversation=group,
                event_type='member_added',
                actor=request.user,
                target_user=user_to_add
            )
            
            # Update group encryption
            from apps.encryption.utils import add_member_to_group_encryption
            add_member_to_group_encryption(group, user_to_add)
        
        return Response({
            'message': 'Member added successfully',
            'participant': ConversationParticipantSerializer(new_participant).data
        })
        
    except User.DoesNotExist:
        return Response({'error': 'User not found'}, status=status.HTTP_404_NOT_FOUND)

@api_view(['DELETE'])
@permission_classes([permissions.IsAuthenticated])
def remove_group_member(request, conversation_id, user_id):
    """Remove a member from a group"""
    group = get_object_or_404(Conversation, id=conversation_id, type='group')
    
    # Check permissions
    participant = group.participants.filter(user=request.user, is_active=True).first()
    if not participant or not participant.can_remove_members:
        return Response({'error': 'Permission denied'}, status=status.HTTP_403_FORBIDDEN)
    
    try:
        member_to_remove = group.participants.get(user_id=user_id, is_active=True)
        
        # Cannot remove group creator unless transferring ownership
        if member_to_remove.user == group.created_by and member_to_remove.role == 'admin':
            admins_count = group.participants.filter(role='admin', is_active=True).count()
            if admins_count <= 1:
                return Response({'error': 'Cannot remove the last admin'}, status=status.HTTP_400_BAD_REQUEST)
        
        with transaction.atomic():
            # Deactivate member
            member_to_remove.is_active = False
            member_to_remove.save()
            
            # Create group event
            GroupEvent.objects.create(
                conversation=group,
                event_type='member_removed',
                actor=request.user,
                target_user=member_to_remove.user
            )
            
            # Rotate group encryption keys
            from apps.encryption.utils import rotate_group_keys
            rotate_group_keys(group, reason='member_removed')
        
        return Response({'message': 'Member removed successfully'})
        
    except ConversationParticipant.DoesNotExist:
        return Response({'error': 'Member not found'}, status=status.HTTP_404_NOT_FOUND)

@api_view(['PUT'])
@permission_classes([permissions.IsAuthenticated])
def update_group_info(request, conversation_id):
    """Update group information"""
    group = get_object_or_404(Conversation, id=conversation_id, type='group')
    
    # Check permissions
    participant = group.participants.filter(user=request.user, is_active=True).first()
    if not participant or not participant.can_edit_group_info:
        return Response({'error': 'Permission denied'}, status=status.HTTP_403_FORBIDDEN)
    
    serializer = UpdateGroupSerializer(group, data=request.data, partial=True)
    if serializer.is_valid():
        updated_group = serializer.save()
        
        # Create group event
        GroupEvent.objects.create(
            conversation=group,
            event_type='group_info_updated',
            actor=request.user,
            event_data=request.data
        )
        
        return Response(ConversationSerializer(updated_group, context={'request': request}).data)
    
    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def leave_group(request, conversation_id):
    """Leave a group conversation"""
    group = get_object_or_404(Conversation, id=conversation_id, type='group')
    
    try:
        participant = group.participants.get(user=request.user, is_active=True)
        
        # Check if user is the last admin
        if participant.role == 'admin':
            admins_count = group.participants.filter(role='admin', is_active=True).count()
            if admins_count <= 1:
                return Response({'error': 'Cannot leave group as the last admin. Transfer admin role first.'}, 
                              status=status.HTTP_400_BAD_REQUEST)
        
        with transaction.atomic():
            # Deactivate participation
            participant.is_active = False
            participant.save()
            
            # Create group event
            GroupEvent.objects.create(
                conversation=group,
                event_type='member_left',
                actor=request.user,
                target_user=request.user
            )
            
            # Rotate group encryption keys
            from apps.encryption.utils import rotate_group_keys
            rotate_group_keys(group, reason='member_removed')
        
        return Response({'message': 'Left group successfully'})
        
    except ConversationParticipant.DoesNotExist:
        return Response({'error': 'Not a member of this group'}, status=status.HTTP_404_NOT_FOUND)
```

### Step 4: Group Serializers

```python
# backend/apps/messaging/serializers.py - Add group-specific serializers

class CreateGroupSerializer(serializers.Serializer):
    name = serializers.CharField(max_length=100)
    description = serializers.CharField(required=False, allow_blank=True)
    member_ids = serializers.ListField(
        child=serializers.UUIDField(),
        required=False,
        default=list
    )
    max_participants = serializers.IntegerField(min_value=2, max_value=500, default=50)
    is_public = serializers.BooleanField(default=False)
    
    def validate_member_ids(self, value):
        if len(value) > 49:  # Creator + 49 others = 50 max
            raise serializers.ValidationError("Too many initial members")
        
        # Verify all user IDs exist
        existing_users = User.objects.filter(id__in=value).count()
        if existing_users != len(value):
            raise serializers.ValidationError("One or more user IDs are invalid")
        
        return value

class UpdateGroupSerializer(serializers.ModelSerializer):
    class Meta:
        model = Conversation
        fields = ['name', 'description', 'avatar_url', 'group_settings']
    
    def validate_name(self, value):
        if len(value.strip()) < 1:
            raise serializers.ValidationError("Group name cannot be empty")
        return value.strip()

class GroupEventSerializer(serializers.ModelSerializer):
    actor = UserBasicSerializer(read_only=True)
    target_user = UserBasicSerializer(read_only=True)
    
    class Meta:
        model = GroupEvent
        fields = ['id', 'event_type', 'actor', 'target_user', 'event_data', 'created_at']

class GroupInviteSerializer(serializers.ModelSerializer):
    invited_by = UserBasicSerializer(read_only=True)
    conversation = ConversationSerializer(read_only=True)
    
    class Meta:
        model = GroupInvite
        fields = ['id', 'conversation', 'invited_by', 'invite_code', 'expires_at', 'created_at']
```

## Frontend Implementation

### Step 5: Group Management Components

```typescript
// frontend/src/components/Group/GroupSettings.tsx
import React, { useState } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Chip,
  Avatar,
  Box,
  Typography,
} from '@mui/material';
import {
  PersonAdd as PersonAddIcon,
  PersonRemove as PersonRemoveIcon,
  AdminPanelSettings as AdminIcon,
} from '@mui/icons-material';

interface GroupSettingsProps {
  open: boolean;
  onClose: () => void;
  group: any;
  currentUser: any;
  onUpdateGroup: (data: any) => void;
  onAddMember: (userId: string) => void;
  onRemoveMember: (userId: string) => void;
  onUpdateMemberRole: (userId: string, role: string) => void;
}

const GroupSettings: React.FC<GroupSettingsProps> = ({
  open,
  onClose,
  group,
  currentUser,
  onUpdateGroup,
  onAddMember,
  onRemoveMember,
  onUpdateMemberRole,
}) => {
  const [groupName, setGroupName] = useState(group?.name || '');
  const [groupDescription, setGroupDescription] = useState(group?.description || '');
  const [isEditing, setIsEditing] = useState(false);

  const currentUserParticipant = group?.participants?.find(
    (p: any) => p.user.id === currentUser.id
  );

  const canManageGroup = currentUserParticipant?.can_edit_group_info;
  const canManageMembers = currentUserParticipant?.can_add_members || currentUserParticipant?.can_remove_members;

  const handleSaveGroupInfo = () => {
    onUpdateGroup({
      name: groupName,
      description: groupDescription,
    });
    setIsEditing(false);
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'admin':
        return 'error';
      case 'moderator':
        return 'warning';
      default:
        return 'default';
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>Group Settings</DialogTitle>
      <DialogContent>
        {/* Group Information */}
        <Box sx={{ mb: 3 }}>
          <Typography variant="h6" gutterBottom>
            Group Information
          </Typography>
          
          {isEditing ? (
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
              <TextField
                label="Group Name"
                value={groupName}
                onChange={(e) => setGroupName(e.target.value)}
                fullWidth
              />
              <TextField
                label="Description"
                value={groupDescription}
                onChange={(e) => setGroupDescription(e.target.value)}
                multiline
                rows={3}
                fullWidth
              />
            </Box>
          ) : (
            <Box>
              <Typography variant="body1" gutterBottom>
                <strong>Name:</strong> {group?.name}
              </Typography>
              <Typography variant="body1" gutterBottom>
                <strong>Description:</strong> {group?.description || 'No description'}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Created by {group?.created_by?.username} • {group?.participants?.length} members
              </Typography>
            </Box>
          )}
        </Box>

        {/* Members List */}
        <Box sx={{ mb: 3 }}>
          <Typography variant="h6" gutterBottom>
            Members ({group?.participants?.length})
          </Typography>
          
          <List>
            {group?.participants?.map((participant: any) => (
              <ListItem key={participant.user.id}>
                <Avatar
                  src={participant.user.profile_picture}
                  sx={{ mr: 2 }}
                >
                  {participant.user.first_name[0]}
                </Avatar>
                
                <ListItemText
                  primary={`${participant.user.first_name} ${participant.user.last_name}`}
                  secondary={`@${participant.user.username}`}
                />
                
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Chip
                    label={participant.role}
                    size="small"
                    color={getRoleColor(participant.role)}
                    icon={participant.role === 'admin' ? <AdminIcon /> : undefined}
                  />
                  
                  {canManageMembers && participant.user.id !== currentUser.id && (
                    <IconButton
                      size="small"
                      onClick={() => onRemoveMember(participant.user.id)}
                      color="error"
                    >
                      <PersonRemoveIcon />
                    </IconButton>
                  )}
                </Box>
              </ListItem>
            ))}
          </List>
        </Box>

        {/* Group Actions */}
        {canManageGroup && (
          <Box sx={{ mb: 2 }}>
            <Typography variant="h6" gutterBottom>
              Group Actions
            </Typography>
            
            <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
              <Button
                variant="outlined"
                startIcon={<PersonAddIcon />}
                onClick={() => {/* Open add member dialog */}}
              >
                Add Member
              </Button>
              
              <Button
                variant="outlined"
                onClick={() => {/* Generate invite link */}}
              >
                Generate Invite Link
              </Button>
            </Box>
          </Box>
        )}
      </DialogContent>
      
      <DialogActions>
        {isEditing ? (
          <>
            <Button onClick={() => setIsEditing(false)}>Cancel</Button>
            <Button onClick={handleSaveGroupInfo} variant="contained">
              Save Changes
            </Button>
          </>
        ) : (
          <>
            <Button onClick={onClose}>Close</Button>
            {canManageGroup && (
              <Button onClick={() => setIsEditing(true)} variant="contained">
                Edit Group
              </Button>
            )}
          </>
        )}
      </DialogActions>
    </Dialog>
  );
};

export default GroupSettings;
```

## Group Encryption Implementation

### Step 6: Group Encryption Utils

```python
# backend/apps/encryption/utils.py
from .models import GroupSession, GroupMemberKey, GroupKeyRotation
from cryptography.fernet import Fernet
import base64

def initialize_group_encryption(conversation):
    """Initialize encryption for a new group"""
    # Generate group session
    session_id = f"group_{conversation.id}_{int(time.time())}"
    group_session = GroupSession.objects.create(
        conversation=conversation,
        session_id=session_id,
        current_epoch=0
    )
    
    # Generate group key
    group_key = Fernet.generate_key()
    
    # Encrypt group key for each member
    for participant in conversation.participants.filter(is_active=True):
        # Get member's public key
        member_key_bundle = participant.user.key_bundle
        if member_key_bundle:
            # Encrypt group key with member's public key
            encrypted_group_key = encrypt_for_member(group_key, member_key_bundle.identity_public_key)
            
            GroupMemberKey.objects.create(
                group_session=group_session,
                member=participant.user,
                encrypted_group_key=encrypted_group_key,
                epoch=0
            )

def add_member_to_group_encryption(conversation, new_member):
    """Add a new member to group encryption"""
    group_session = conversation.group_session
    
    # Get current group key (this would be more complex in practice)
    current_epoch = group_session.current_epoch
    
    # Encrypt group key for new member
    member_key_bundle = new_member.key_bundle
    if member_key_bundle:
        # In practice, you'd need to get the current group key
        # This is simplified for demonstration
        group_key = get_current_group_key(group_session)
        encrypted_group_key = encrypt_for_member(group_key, member_key_bundle.identity_public_key)
        
        GroupMemberKey.objects.create(
            group_session=group_session,
            member=new_member,
            encrypted_group_key=encrypted_group_key,
            epoch=current_epoch
        )

def rotate_group_keys(conversation, reason='scheduled'):
    """Rotate group encryption keys"""
    group_session = conversation.group_session
    old_epoch = group_session.current_epoch
    new_epoch = old_epoch + 1
    
    # Generate new group key
    new_group_key = Fernet.generate_key()
    
    # Encrypt new key for all active members
    for participant in conversation.participants.filter(is_active=True):
        member_key_bundle = participant.user.key_bundle
        if member_key_bundle:
            encrypted_group_key = encrypt_for_member(new_group_key, member_key_bundle.identity_public_key)
            
            GroupMemberKey.objects.create(
                group_session=group_session,
                member=participant.user,
                encrypted_group_key=encrypted_group_key,
                epoch=new_epoch
            )
    
    # Update session epoch
    group_session.current_epoch = new_epoch
    group_session.save()
    
    # Record key rotation
    GroupKeyRotation.objects.create(
        group_session=group_session,
        old_epoch=old_epoch,
        new_epoch=new_epoch,
        reason=reason
    )

def encrypt_for_member(data, member_public_key):
    """Encrypt data for a specific member"""
    # This is a simplified implementation
    # In practice, you'd use proper public key encryption
    return base64.b64encode(data).decode('utf-8')

def get_current_group_key(group_session):
    """Get the current group key (simplified)"""
    # In practice, this would involve proper key management
    return Fernet.generate_key()
```

## Integration Points

### Socket.io Group Events
- Group member additions/removals
- Group info updates
- Group-specific message broadcasting

### Frontend State Management
- Group member list updates
- Permission-based UI rendering
- Group encryption key management

## Acceptance Criteria

### Phase 4 Completion Checklist
- [ ] Group creation and management working
- [ ] Member addition/removal functional
- [ ] Role-based permissions implemented
- [ ] Group encryption working
- [ ] Group settings UI complete
- [ ] Group events tracking
- [ ] Invite system functional

### Testing Requirements
- [ ] Group management API tests
- [ ] Permission system tests
- [ ] Group encryption tests
- [ ] UI component tests
- [ ] Integration tests for group flows

## Common Issues & Troubleshooting

### Permission Conflicts
- Verify role-based permission logic
- Check admin transfer scenarios
- Test edge cases with last admin

### Group Encryption Issues
- Ensure key rotation on member changes
- Verify all members receive new keys
- Handle offline member scenarios

### Performance Considerations
- Optimize member list queries
- Implement pagination for large groups
- Cache group permissions

## Next Phase Dependencies
- Group chat fully functional
- Group encryption stable
- Member management working
- UI responsive for group operations

This phase enables multi-party conversations with proper security and management. Ensure thorough testing before proceeding to Phase 5 (Media Sharing).
