{"info": {"_postman_id": "chatapp-socket-server-collection", "name": "ChatApp Socket Server API", "description": "Real-time messaging API with end-to-end encryption support. This collection provides comprehensive testing capabilities for the ChatApp Socket Server including authentication, messaging, conversation management, typing indicators, and encryption operations.", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "chatapp-team"}, "item": [{"name": "Connection & Authentication", "item": [{"name": "Connect to Socket Server", "request": {"method": "WEBSOCKET", "header": [], "url": {"raw": "{{baseUrl}}", "protocol": "ws", "host": ["{{baseUrl}}"]}, "description": "Establish WebSocket connection to the server. After connection, send authentication payload."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["// Test connection establishment", "pm.test('WebSocket connection established', function () {", "    pm.expect(pm.response.code).to.be.oneOf([101, 200]);", "});"], "type": "text/javascript"}}]}, {"name": "Authenticate Connection", "request": {"method": "WEBSOCKET", "header": [], "body": {"mode": "raw", "raw": "{\n  \"token\": \"{{authToken}}\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}", "protocol": "ws", "host": ["{{baseUrl}}"]}, "description": "Send JWT authentication token to establish authenticated session."}, "response": [{"name": "Authentication Success", "originalRequest": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "{\n  \"token\": \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...\"\n}"}, "url": {"raw": "{{baseUrl}}", "host": ["{{baseUrl}}"]}}, "status": "Switching Protocols", "code": 101, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\n  \"event\": \"authenticated\",\n  \"userId\": \"550e8400-e29b-41d4-a716-************\"\n}"}, {"name": "Authentication Error", "originalRequest": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "{\n  \"token\": \"invalid-token\"\n}"}, "url": {"raw": "{{baseUrl}}", "host": ["{{baseUrl}}"]}}, "status": "Unauthorized", "code": 401, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\n  \"error\": \"Invalid authentication token\",\n  \"code\": \"AUTH_ERROR\"\n}"}]}], "description": "WebSocket connection establishment and JWT authentication"}, {"name": "Messaging Operations", "item": [{"name": "Send Plaintext Message", "request": {"method": "WEBSOCKET", "header": [], "body": {"mode": "raw", "raw": "{\n  \"event\": \"send_message\",\n  \"conversationId\": \"{{conversationId}}\",\n  \"tempId\": \"temp-{{$randomUUID}}\",\n  \"messageType\": \"TEXT\",\n  \"content\": \"Hello, this is a test message!\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}", "protocol": "ws", "host": ["{{baseUrl}}"]}, "description": "Send a plaintext message to a conversation (Phase 2 compatibility)"}, "response": [{"name": "Message Sent Successfully", "originalRequest": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "{\n  \"event\": \"send_message\",\n  \"conversationId\": \"550e8400-e29b-41d4-a716-************\",\n  \"tempId\": \"temp-123\",\n  \"messageType\": \"TEXT\",\n  \"content\": \"Hello, this is a test message!\"\n}"}, "url": {"raw": "{{baseUrl}}", "host": ["{{baseUrl}}"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\n  \"event\": \"message_sent\",\n  \"tempId\": \"temp-123\",\n  \"messageId\": \"550e8400-e29b-41d4-a716-************\",\n  \"status\": \"DELIVERED\",\n  \"isEncrypted\": false\n}"}]}, {"name": "Send Encrypted Message", "request": {"method": "WEBSOCKET", "header": [], "body": {"mode": "raw", "raw": "{\n  \"event\": \"send_message\",\n  \"conversationId\": \"{{conversationId}}\",\n  \"tempId\": \"temp-{{$randomUUID}}\",\n  \"messageType\": \"TEXT\",\n  \"encryptedContent\": \"dGhpcyBpcyBmYWtlIGVuY3J5cHRlZCBjb250ZW50\",\n  \"iv\": \"cmFuZG9tSVY5NmJpdA==\",\n  \"senderRatchetKey\": \"ZmFrZVJhdGNoZXRLZXlTUEtJRm9ybWF0\",\n  \"messageNumber\": 1,\n  \"previousChainLength\": 0\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}", "protocol": "ws", "host": ["{{baseUrl}}"]}, "description": "Send an encrypted message using Double Ratchet protocol (Phase 3)"}, "response": [{"name": "Encrypted Message Sent", "originalRequest": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "{\n  \"event\": \"send_message\",\n  \"conversationId\": \"550e8400-e29b-41d4-a716-************\",\n  \"tempId\": \"temp-456\",\n  \"messageType\": \"TEXT\",\n  \"encryptedContent\": \"dGhpcyBpcyBmYWtlIGVuY3J5cHRlZCBjb250ZW50\",\n  \"iv\": \"cmFuZG9tSVY5NmJpdA==\",\n  \"senderRatchetKey\": \"ZmFrZVJhdGNoZXRLZXlTUEtJRm9ybWF0\",\n  \"messageNumber\": 1,\n  \"previousChainLength\": 0\n}"}, "url": {"raw": "{{baseUrl}}", "host": ["{{baseUrl}}"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\n  \"event\": \"message_sent\",\n  \"tempId\": \"temp-456\",\n  \"messageId\": \"550e8400-e29b-41d4-a716-************\",\n  \"status\": \"DELIVERED\",\n  \"isEncrypted\": true\n}"}]}, {"name": "Send Image Message", "request": {"method": "WEBSOCKET", "header": [], "body": {"mode": "raw", "raw": "{\n  \"event\": \"send_message\",\n  \"conversationId\": \"{{conversationId}}\",\n  \"tempId\": \"temp-{{$randomUUID}}\",\n  \"messageType\": \"IMAGE\",\n  \"content\": \"https://example.com/image.jpg\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}", "protocol": "ws", "host": ["{{baseUrl}}"]}, "description": "Send an image message with URL reference"}, "response": []}], "description": "Message sending operations for both plaintext and encrypted messages"}, {"name": "Conversation Management", "item": [{"name": "Join Conversation", "request": {"method": "WEBSOCKET", "header": [], "body": {"mode": "raw", "raw": "{\n  \"event\": \"join_conversation\",\n  \"conversationId\": \"{{conversationId}}\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}", "protocol": "ws", "host": ["{{baseUrl}}"]}, "description": "Join a conversation to start receiving messages and participate in real-time communication"}, "response": [{"name": "Successfully Joined", "originalRequest": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "{\n  \"event\": \"join_conversation\",\n  \"conversationId\": \"550e8400-e29b-41d4-a716-************\"\n}"}, "url": {"raw": "{{baseUrl}}", "host": ["{{baseUrl}}"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\n  \"event\": \"conversation_joined\",\n  \"conversationId\": \"550e8400-e29b-41d4-a716-************\",\n  \"participants\": [\n    {\n      \"id\": \"user1\",\n      \"username\": \"testuser1\"\n    },\n    {\n      \"id\": \"user2\",\n      \"username\": \"testuser2\"\n    }\n  ]\n}"}]}, {"name": "Leave Conversation", "request": {"method": "WEBSOCKET", "header": [], "body": {"mode": "raw", "raw": "{\n  \"event\": \"leave_conversation\",\n  \"conversationId\": \"{{conversationId}}\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}", "protocol": "ws", "host": ["{{baseUrl}}"]}, "description": "Leave a conversation to stop receiving messages from that conversation"}, "response": [{"name": "Successfully Left", "originalRequest": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "{\n  \"event\": \"leave_conversation\",\n  \"conversationId\": \"550e8400-e29b-41d4-a716-************\"\n}"}, "url": {"raw": "{{baseUrl}}", "host": ["{{baseUrl}}"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\n  \"event\": \"conversation_left\",\n  \"conversationId\": \"550e8400-e29b-41d4-a716-************\"\n}"}]}], "description": "Operations for joining and leaving conversations"}, {"name": "Typing Indicators", "item": [{"name": "Start Typing", "request": {"method": "WEBSOCKET", "header": [], "body": {"mode": "raw", "raw": "{\n  \"event\": \"typing_start\",\n  \"conversationId\": \"{{conversationId}}\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}", "protocol": "ws", "host": ["{{baseUrl}}"]}, "description": "Indicate that the user has started typing in a conversation"}, "response": []}, {"name": "Stop Typing", "request": {"method": "WEBSOCKET", "header": [], "body": {"mode": "raw", "raw": "{\n  \"event\": \"typing_stop\",\n  \"conversationId\": \"{{conversationId}}\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}", "protocol": "ws", "host": ["{{baseUrl}}"]}, "description": "Indicate that the user has stopped typing in a conversation"}, "response": [{"name": "Typing Indicator Broadcast", "originalRequest": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "{\n  \"event\": \"typing_start\",\n  \"conversationId\": \"550e8400-e29b-41d4-a716-************\"\n}"}, "url": {"raw": "{{baseUrl}}", "host": ["{{baseUrl}}"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\n  \"event\": \"typing_indicator\",\n  \"conversationId\": \"550e8400-e29b-41d4-a716-************\",\n  \"userId\": \"{{userId}}\",\n  \"username\": \"testuser\",\n  \"isTyping\": true\n}"}]}], "description": "Real-time typing indicators for enhanced user experience"}, {"name": "Encryption Operations", "item": [{"name": "Request Key Exchange", "request": {"method": "WEBSOCKET", "header": [], "body": {"mode": "raw", "raw": "{\n  \"event\": \"key_exchange_request\",\n  \"targetUserId\": \"{{targetUserId}}\",\n  \"conversationId\": \"{{conversationId}}\",\n  \"ephemeralPublicKey\": \"ZXBoZW1lcmFsS2V5RXhhbXBsZQ==\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}", "protocol": "ws", "host": ["{{baseUrl}}"]}, "description": "Request key bundle from another user for establishing encrypted communication"}, "response": [{"name": "Key Exchange Success", "originalRequest": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "{\n  \"event\": \"key_exchange_request\",\n  \"targetUserId\": \"550e8400-e29b-41d4-a716-************\",\n  \"conversationId\": \"550e8400-e29b-41d4-a716-************\",\n  \"ephemeralPublicKey\": \"ZXBoZW1lcmFsS2V5RXhhbXBsZQ==\"\n}"}, "url": {"raw": "{{baseUrl}}", "host": ["{{baseUrl}}"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\n  \"event\": \"key_exchange_response\",\n  \"success\": true,\n  \"keyBundle\": {\n    \"identityPublicKey\": \"aWRlbnRpdHlLZXlFeGFtcGxl\",\n    \"signedPrekey\": {\n      \"id\": 1,\n      \"publicKey\": \"c2lnbmVkUHJla2V5RXhhbXBsZQ==\",\n      \"signature\": \"c2lnbmF0dXJlRXhhbXBsZQ==\"\n    },\n    \"oneTimePrekey\": {\n      \"id\": 1,\n      \"publicKey\": \"b25lVGltZVByZWtleUV4YW1wbGU=\"\n    }\n  }\n}"}, {"name": "Key Exchange Error", "originalRequest": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "{\n  \"event\": \"key_exchange_request\",\n  \"targetUserId\": \"invalid-user-id\",\n  \"conversationId\": \"550e8400-e29b-41d4-a716-************\",\n  \"ephemeralPublicKey\": \"ZXBoZW1lcmFsS2V5RXhhbXBsZQ==\"\n}"}, "url": {"raw": "{{baseUrl}}", "host": ["{{baseUrl}}"]}}, "status": "Not Found", "code": 404, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\n  \"event\": \"key_exchange_response\",\n  \"error\": \"User not found\",\n  \"code\": \"USER_NOT_FOUND\"\n}"}]}, {"name": "Check Encryption Status", "request": {"method": "WEBSOCKET", "header": [], "body": {"mode": "raw", "raw": "{\n  \"event\": \"encryption_status_check\",\n  \"conversationId\": \"{{conversationId}}\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}", "protocol": "ws", "host": ["{{baseUrl}}"]}, "description": "Check the encryption status of a conversation and its participants"}, "response": [{"name": "Encryption Status Response", "originalRequest": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "{\n  \"event\": \"encryption_status_check\",\n  \"conversationId\": \"550e8400-e29b-41d4-a716-************\"\n}"}, "url": {"raw": "{{baseUrl}}", "host": ["{{baseUrl}}"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\n  \"event\": \"encryption_status_response\",\n  \"conversationId\": \"550e8400-e29b-41d4-a716-************\",\n  \"isEncrypted\": true,\n  \"participants\": [\n    {\n      \"id\": \"{{userId}}\",\n      \"username\": \"testuser\",\n      \"hasEncryption\": true\n    },\n    {\n      \"id\": \"{{targetUserId}}\",\n      \"username\": \"targetuser\",\n      \"hasEncryption\": true\n    }\n  ]\n}"}]}], "description": "End-to-end encryption operations including key exchange and status checking"}, {"name": "<PERSON><PERSON><PERSON>", "item": [{"name": "Invalid Message Format", "request": {"method": "WEBSOCKET", "header": [], "body": {"mode": "raw", "raw": "{\n  \"invalid\": \"message\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}", "protocol": "ws", "host": ["{{baseUrl}}"]}, "description": "Test error handling for invalid message format"}, "response": [{"name": "Validation Error", "originalRequest": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "{\n  \"invalid\": \"message\"\n}"}, "url": {"raw": "{{baseUrl}}", "host": ["{{baseUrl}}"]}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\n  \"error\": \"Invalid message format\",\n  \"code\": \"VALIDATION_ERROR\",\n  \"details\": {\n    \"field\": \"event\",\n    \"message\": \"Event field is required\"\n  }\n}"}]}, {"name": "Unauthorized Access", "request": {"method": "WEBSOCKET", "header": [], "body": {"mode": "raw", "raw": "{\n  \"event\": \"send_message\",\n  \"conversationId\": \"unauthorized-conversation\",\n  \"content\": \"This should fail\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}", "protocol": "ws", "host": ["{{baseUrl}}"]}, "description": "Test error handling for unauthorized conversation access"}, "response": [{"name": "Access Denied", "originalRequest": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "{\n  \"event\": \"send_message\",\n  \"conversationId\": \"unauthorized-conversation\",\n  \"content\": \"This should fail\"\n}"}, "url": {"raw": "{{baseUrl}}", "host": ["{{baseUrl}}"]}}, "status": "Forbidden", "code": 403, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\n  \"error\": \"Access denied to conversation\",\n  \"code\": \"ACCESS_DENIED\"\n}"}]}], "description": "Error handling and edge case testing scenarios"}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Pre-request script for the entire collection", "console.log('Starting ChatApp Socket Server API test');", "", "// Validate required environment variables", "const requiredVars = ['baseUrl', 'authToken', 'userId', 'conversationId'];", "const missingVars = requiredVars.filter(varName => !pm.environment.get(varName));", "", "if (missingVars.length > 0) {", "    console.warn('Missing environment variables:', missingVars.join(', '));", "    console.warn('Please set these variables in your environment for proper testing.');", "}", "", "// Set dynamic variables", "pm.globals.set('timestamp', Date.now());"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// Global test script for the entire collection", "pm.test('Response time is acceptable', function () {", "    pm.expect(pm.response.responseTime).to.be.below(5000);", "});", "", "// Log response for debugging", "console.log('Response status:', pm.response.status);", "console.log('Response time:', pm.response.responseTime + 'ms');"]}}], "variable": [{"key": "baseUrl", "value": "ws://localhost:3001", "type": "string", "description": "Base URL for the WebSocket server"}, {"key": "authToken", "value": "", "type": "string", "description": "JWT authentication token"}, {"key": "userId", "value": "", "type": "string", "description": "Current user ID"}, {"key": "conversationId", "value": "", "type": "string", "description": "Target conversation ID for testing"}, {"key": "targetUserId", "value": "", "type": "string", "description": "Target user ID for encryption testing"}]}