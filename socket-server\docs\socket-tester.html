<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ChatApp Socket Server Tester</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.socket.io/4.7.4/socket.io.min.js"></script>
    <style>
        .message-container {
            max-height: 400px;
            overflow-y: auto;
        }
        .request-message {
            background-color: #e3f2fd;
            border-left: 4px solid #2196f3;
        }
        .response-message {
            background-color: #f3e5f5;
            border-left: 4px solid #9c27b0;
        }
        .error-message {
            background-color: #ffebee;
            border-left: 4px solid #f44336;
        }
        .success-message {
            background-color: #e8f5e8;
            border-left: 4px solid #4caf50;
        }
    </style>
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <h1 class="text-3xl font-bold text-center mb-8 text-gray-800">ChatApp Socket Server Tester</h1>
        
        <!-- Connection Section -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4 text-gray-700">Connection Settings</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Socket URL</label>
                    <input type="text" id="socketUrl" value="http://localhost:7000" 
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Authentication Token</label>
                    <input type="text" id="authToken" placeholder="JWT Token" 
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
            </div>
            <div class="flex gap-2">
                <button id="connectBtn" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md transition-colors">
                    Connect
                </button>
                <button id="disconnectBtn" class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-md transition-colors" disabled>
                    Disconnect
                </button>
                <span id="connectionStatus" class="flex items-center px-3 py-2 text-sm font-medium">
                    <span class="w-2 h-2 bg-red-500 rounded-full mr-2"></span>
                    Disconnected
                </span>
            </div>
        </div>

        <!-- Socket Events Section -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4 text-gray-700">Socket Events</h2>
            
            <!-- Authentication -->
            <div class="mb-6">
                <h3 class="text-lg font-medium mb-3 text-gray-600">Authentication</h3>
                <p class="text-sm text-gray-600 mb-2">Note: Authentication happens automatically during connection if token is provided in the auth field above.</p>
                <button onclick="authenticate()" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-md mr-2 mb-2">
                    Manual Authenticate
                </button>
            </div>

            <!-- Conversation Management -->
            <div class="mb-6">
                <h3 class="text-lg font-medium mb-3 text-gray-600">Conversation Management</h3>
                <div class="flex flex-wrap gap-2 mb-3">
                    <input type="text" id="conversationId" placeholder="Conversation ID" 
                           class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                <div class="flex flex-wrap gap-2">
                    <button onclick="joinConversation()" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md">
                        Join Conversation
                    </button>
                    <button onclick="leaveConversation()" class="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-md">
                        Leave Conversation
                    </button>
                </div>
            </div>

            <!-- Messaging -->
            <div class="mb-6">
                <h3 class="text-lg font-medium mb-3 text-gray-600">Messaging</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-3">
                    <input type="text" id="messageContent" placeholder="Message content" 
                           class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <select id="messageType" class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="TEXT">Text</option>
                        <option value="IMAGE">Image</option>
                    </select>
                </div>
                <div class="flex flex-wrap gap-2">
                    <button onclick="sendPlaintextMessage()" class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-md">
                        Send Plaintext Message
                    </button>
                    <button onclick="sendEncryptedMessage()" class="bg-indigo-500 hover:bg-indigo-600 text-white px-4 py-2 rounded-md">
                        Send Encrypted Message
                    </button>
                </div>
            </div>

            <!-- Typing Indicators -->
            <div class="mb-6">
                <h3 class="text-lg font-medium mb-3 text-gray-600">Typing Indicators</h3>
                <div class="flex flex-wrap gap-2">
                    <button onclick="startTyping()" class="bg-yellow-500 hover:bg-yellow-600 text-white px-4 py-2 rounded-md">
                        Start Typing
                    </button>
                    <button onclick="stopTyping()" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-md">
                        Stop Typing
                    </button>
                </div>
            </div>

            <!-- Encryption Operations -->
            <div class="mb-6">
                <h3 class="text-lg font-medium mb-3 text-gray-600">Encryption Operations</h3>
                <div class="flex flex-wrap gap-2 mb-3">
                    <input type="text" id="targetUserId" placeholder="Target User ID" 
                           class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                <div class="flex flex-wrap gap-2">
                    <button onclick="requestKeyExchange()" class="bg-teal-500 hover:bg-teal-600 text-white px-4 py-2 rounded-md">
                        Request Key Exchange
                    </button>
                    <button onclick="checkEncryptionStatus()" class="bg-cyan-500 hover:bg-cyan-600 text-white px-4 py-2 rounded-md">
                        Check Encryption Status
                    </button>
                </div>
            </div>
        </div>

        <!-- Messages Display -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex justify-between items-center mb-4">
                <h2 class="text-xl font-semibold text-gray-700">Messages</h2>
                <button onclick="clearMessages()" class="bg-gray-500 hover:bg-gray-600 text-white px-3 py-1 rounded-md text-sm">
                    Clear
                </button>
            </div>
            <div id="messagesContainer" class="message-container space-y-3">
                <!-- Messages will be displayed here -->
            </div>
        </div>
    </div>

    <script>
        let socket = null;
        let isConnected = false;

        // Utility functions
        function generateTempId() {
            return 'temp-' + Math.random().toString(36).substr(2, 9);
        }

        function addMessage(type, event, data, isOutgoing = false) {
            const container = document.getElementById('messagesContainer');
            const messageDiv = document.createElement('div');
            const timestamp = new Date().toLocaleTimeString();
            
            let bgClass = '';
            let label = '';
            
            if (type === 'request') {
                bgClass = 'request-message';
                label = isOutgoing ? 'SENT' : 'REQUEST';
            } else if (type === 'response') {
                bgClass = 'response-message';
                label = 'RESPONSE';
            } else if (type === 'error') {
                bgClass = 'error-message';
                label = 'ERROR';
            } else if (type === 'success') {
                bgClass = 'success-message';
                label = 'SUCCESS';
            }
            
            messageDiv.className = `p-4 rounded-md ${bgClass}`;
            messageDiv.innerHTML = `
                <div class="flex justify-between items-start mb-2">
                    <span class="font-semibold text-sm">${label}: ${event}</span>
                    <span class="text-xs text-gray-500">${timestamp}</span>
                </div>
                <pre class="text-sm overflow-x-auto whitespace-pre-wrap">${JSON.stringify(data, null, 2)}</pre>
            `;
            
            container.appendChild(messageDiv);
            container.scrollTop = container.scrollHeight;
        }

        function clearMessages() {
            document.getElementById('messagesContainer').innerHTML = '';
        }

        function updateConnectionStatus(connected) {
            isConnected = connected;
            const statusElement = document.getElementById('connectionStatus');
            const connectBtn = document.getElementById('connectBtn');
            const disconnectBtn = document.getElementById('disconnectBtn');
            
            if (connected) {
                statusElement.innerHTML = '<span class="w-2 h-2 bg-green-500 rounded-full mr-2"></span>Connected';
                statusElement.className = 'flex items-center px-3 py-2 text-sm font-medium text-green-700';
                connectBtn.disabled = true;
                disconnectBtn.disabled = false;
            } else {
                statusElement.innerHTML = '<span class="w-2 h-2 bg-red-500 rounded-full mr-2"></span>Disconnected';
                statusElement.className = 'flex items-center px-3 py-2 text-sm font-medium text-red-700';
                connectBtn.disabled = false;
                disconnectBtn.disabled = true;
            }
        }

        // Connection functions
        function connect() {
            const url = document.getElementById('socketUrl').value;
            const token = document.getElementById('authToken').value;
            
            if (!url) {
                alert('Please enter a socket URL');
                return;
            }

            try {
                const socketOptions = {
                    transports: ['websocket', 'polling']
                };
                
                // Add auth token if provided (matches frontend implementation)
                if (token) {
                    socketOptions.auth = {
                        token: token
                    };
                }
                
                socket = io(url, socketOptions);

                socket.on('connect', () => {
                    updateConnectionStatus(true);
                    addMessage('success', 'connect', { message: 'Connected to server' });
                });

                socket.on('disconnect', () => {
                    updateConnectionStatus(false);
                    addMessage('error', 'disconnect', { message: 'Disconnected from server' });
                });

                socket.on('connect_error', (error) => {
                    addMessage('error', 'connect_error', { error: error.message });
                });

                // Listen for all possible events
                const events = [
                    'authenticated', 'auth_error', 'message_sent', 'new_message',
                    'conversation_joined', 'conversation_left', 'typing_indicator',
                    'key_exchange_response', 'encryption_status_response'
                ];

                events.forEach(event => {
                    socket.on(event, (data) => {
                        addMessage('response', event, data);
                    });
                });

            } catch (error) {
                addMessage('error', 'connection', { error: error.message });
            }
        }

        function disconnect() {
            if (socket) {
                socket.disconnect();
                socket = null;
                updateConnectionStatus(false);
            }
        }

        // Socket event functions
        function authenticate() {
            if (!socket || !isConnected) {
                alert('Please connect to the server first');
                return;
            }

            const token = document.getElementById('authToken').value;
            if (!token) {
                alert('Please enter an authentication token');
                return;
            }

            // Note: Authentication happens automatically during connection if token is provided
            // This function can be used to re-authenticate or test manual auth
            const payload = { token };
            socket.emit('authenticate', payload);
            addMessage('request', 'authenticate', payload, true);
        }

        function joinConversation() {
            if (!socket || !isConnected) {
                alert('Please connect to the server first');
                return;
            }

            const conversationId = document.getElementById('conversationId').value;
            if (!conversationId) {
                alert('Please enter a conversation ID');
                return;
            }

            const payload = {
                event: 'join_conversation',
                conversationId
            };
            socket.emit('join_conversation', payload);
            addMessage('request', 'join_conversation', payload, true);
        }

        function leaveConversation() {
            if (!socket || !isConnected) {
                alert('Please connect to the server first');
                return;
            }

            const conversationId = document.getElementById('conversationId').value;
            if (!conversationId) {
                alert('Please enter a conversation ID');
                return;
            }

            const payload = {
                event: 'leave_conversation',
                conversationId
            };
            socket.emit('leave_conversation', payload);
            addMessage('request', 'leave_conversation', payload, true);
        }

        function sendPlaintextMessage() {
            if (!socket || !isConnected) {
                alert('Please connect to the server first');
                return;
            }

            const conversationId = document.getElementById('conversationId').value;
            const content = document.getElementById('messageContent').value;
            const messageType = document.getElementById('messageType').value;

            if (!conversationId || !content) {
                alert('Please enter conversation ID and message content');
                return;
            }

            const payload = {
                event: 'send_message',
                conversationId,
                tempId: generateTempId(),
                messageType,
                content
            };
            socket.emit('send_message', payload);
            addMessage('request', 'send_message', payload, true);
        }

        function sendEncryptedMessage() {
            if (!socket || !isConnected) {
                alert('Please connect to the server first');
                return;
            }

            const conversationId = document.getElementById('conversationId').value;
            const content = document.getElementById('messageContent').value;
            const messageType = document.getElementById('messageType').value;

            if (!conversationId || !content) {
                alert('Please enter conversation ID and message content');
                return;
            }

            // Simulate encrypted content (base64 encoded)
            const encryptedContent = btoa(content);
            const iv = btoa('randomIV96bit');
            // Generate a valid SPKI format key (minimum 50 characters base64)
            const senderRatchetKey = btoa('MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA1234567890abcdefghijklmnopqrstuvwxyz');

            const payload = {
                event: 'send_message',
                conversationId,
                tempId: generateTempId(),
                messageType,
                encryptedContent,
                iv,
                senderRatchetKey,
                messageNumber: 1,
                previousChainLength: 0
            };
            socket.emit('send_message', payload);
            addMessage('request', 'send_message (encrypted)', payload, true);
        }

        function startTyping() {
            if (!socket || !isConnected) {
                alert('Please connect to the server first');
                return;
            }

            const conversationId = document.getElementById('conversationId').value;
            if (!conversationId) {
                alert('Please enter a conversation ID');
                return;
            }

            const payload = {
                event: 'typing_start',
                conversationId
            };
            socket.emit('typing_start', payload);
            addMessage('request', 'typing_start', payload, true);
        }

        function stopTyping() {
            if (!socket || !isConnected) {
                alert('Please connect to the server first');
                return;
            }

            const conversationId = document.getElementById('conversationId').value;
            if (!conversationId) {
                alert('Please enter a conversation ID');
                return;
            }

            const payload = {
                event: 'typing_stop',
                conversationId
            };
            socket.emit('typing_stop', payload);
            addMessage('request', 'typing_stop', payload, true);
        }

        function requestKeyExchange() {
            if (!socket || !isConnected) {
                alert('Please connect to the server first');
                return;
            }

            const conversationId = document.getElementById('conversationId').value;
            const targetUserId = document.getElementById('targetUserId').value;

            if (!conversationId || !targetUserId) {
                alert('Please enter conversation ID and target user ID');
                return;
            }

            const payload = {
                event: 'key_exchange_request',
                targetUserId,
                conversationId,
                ephemeralPublicKey: btoa('ephemeralKeyExample')
            };
            socket.emit('key_exchange_request', payload);
            addMessage('request', 'key_exchange_request', payload, true);
        }

        function checkEncryptionStatus() {
            if (!socket || !isConnected) {
                alert('Please connect to the server first');
                return;
            }

            const conversationId = document.getElementById('conversationId').value;
            if (!conversationId) {
                alert('Please enter a conversation ID');
                return;
            }

            const payload = {
                event: 'encryption_status_check',
                conversationId
            };
            socket.emit('encryption_status_check', payload);
            addMessage('request', 'encryption_status_check', payload, true);
        }

        // Event listeners
        document.getElementById('connectBtn').addEventListener('click', connect);
        document.getElementById('disconnectBtn').addEventListener('click', disconnect);

        // Initialize
        updateConnectionStatus(false);
    </script>
</body>
</html>