generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  password               String                    @db.VarChar(128)
  last_login             DateTime?                 @db.Timestamptz(6)
  is_superuser           Boolean
  username               String                    @unique @db.VarChar(150)
  is_staff               Boolean
  is_active              Boolean
  date_joined            DateTime                  @db.Timestamptz(6)
  id                     String                    @id @default(uuid()) @db.Uuid
  email                  String                    @unique @db.VarChar(254)
  firstName              String                    @map("first_name") @db.VarChar(30)
  lastName               String                    @map("last_name") @db.VarChar(30)
  profilePicture         String?                   @map("profile_picture") @db.VarChar(200)
  isVerified             Boolean                   @map("is_verified")
  lastSeen               DateTime                  @updatedAt @map("last_seen") @db.Timestamptz(6)
  createdAt              DateTime                  @map("created_at") @db.Timestamptz(6)
  updatedAt              DateTime                  @updatedAt @map("updated_at") @db.Timestamptz(6)
  conversations          ConversationParticipant[]
  conversationSessions   ConversationSession[]
  django_admin_log       django_admin_log[]
  keyBundleUploadLogs    KeyBundleUploadLog[]
  messageStatuses        MessageStatus[]           @relation("MessageStatuses")
  sentMessages           Message[]                 @relation("MessageSender")
  oneTimePreKeys         OneTimePreKey[]
  keyBundle              UserKeyBundle?
  users_groups           users_groups[]
  users_user_permissions users_user_permissions[]

  @@index([email], map: "users_email_0ea73cca_like")
  @@index([username], map: "users_username_e8658fc8_like")
  @@map("users")
}

model Conversation {
  id                 String                    @id @default(uuid()) @db.Uuid
  type               String                    @db.VarChar(10)
  name               String?                   @db.VarChar(100)
  createdAt          DateTime                  @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt          DateTime                  @updatedAt @map("updated_at") @db.Timestamptz(6)
  participants       ConversationParticipant[]
  encryptionSessions ConversationSession[]
  messages           Message[]

  @@map("conversations")
}

model ConversationParticipant {
  id             String       @id @default(uuid()) @db.Uuid
  role           String       @db.VarChar(10)
  joinedAt       DateTime     @default(now()) @map("joined_at") @db.Timestamptz(6)
  conversationId String       @map("conversation_id") @db.Uuid
  userId         String       @map("user_id") @db.Uuid
  conversation   Conversation @relation(fields: [conversationId], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "conversation_partici_conversation_id_58e662d4_fk_conversat")
  user           User         @relation(fields: [userId], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "conversation_participants_user_id_c65bf2e6_fk_users_id")

  @@unique([conversationId, userId], map: "conversation_participants_conversation_id_user_id_eeefe97d_uniq")
  @@index([conversationId], map: "conversation_participants_conversation_id_58e662d4")
  @@index([userId], map: "conversation_participants_user_id_c65bf2e6")
  @@map("conversation_participants")
}

model Message {
  id                  String          @id @default(uuid()) @db.Uuid
  content             String
  messageType         String          @map("message_type") @db.VarChar(10)
  createdAt           DateTime        @default(now()) @map("created_at") @db.Timestamptz(6)
  updatedAt           DateTime        @updatedAt @map("updated_at") @db.Timestamptz(6)
  conversationId      String          @map("conversation_id") @db.Uuid
  senderId            String          @map("sender_id") @db.Uuid
  encryptedContent    String?         @map("encrypted_content")
  messageNumber       Int             @default(0) @map("message_number")
  previousChainLength Int             @default(0) @map("previous_chain_length")
  senderRatchetKey    String?         @map("sender_ratchet_key")
  iv                  String?
  statuses            MessageStatus[]
  conversation        Conversation    @relation(fields: [conversationId], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "messages_conversation_id_5ef638db_fk_conversations_id")
  sender              User            @relation("MessageSender", fields: [senderId], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "messages_sender_id_dc5a0bbd_fk_users_id")

  @@index([conversationId], map: "messages_conversation_id_5ef638db")
  @@index([senderId], map: "messages_sender_id_dc5a0bbd")
  @@map("messages")
}

enum MessageStatusType {
  SENT
  DELIVERED
  READ
  FAILED

  @@map("MessageStatusType")
}

model MessageStatus {
  id        String            @id @default(uuid()) @db.Uuid
  status    MessageStatusType @default(SENT)
  createdAt DateTime          @map("created_at") @db.Timestamptz(6)
  updatedAt DateTime          @updatedAt @map("updated_at") @db.Timestamptz(6)
  messageId String            @map("message_id") @db.Uuid
  userId    String            @map("user_id") @db.Uuid
  message   Message           @relation(fields: [messageId], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "message_statuses_message_id_497b4101_fk_messages_id")
  user      User              @relation("MessageStatuses", fields: [userId], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "message_statuses_user_id_619e2277_fk_users_id")

  @@unique([messageId, userId], map: "message_statuses_message_id_user_id_d84c4322_uniq")
  @@index([messageId], map: "message_statuses_message_id_497b4101")
  @@index([userId], map: "message_statuses_user_id_619e2277")
  @@map("message_statuses")
}

model UserKeyBundle {
  id                    String   @id @default(uuid()) @db.Uuid
  identityPublicKey     String   @map("identity_public_key")
  signedPrekeyId        Int      @map("signed_prekey_id")
  signedPrekeyPublic    String   @map("signed_prekey_public")
  signedPrekeySignature String   @map("signed_prekey_signature")
  createdAt             DateTime @map("created_at") @db.Timestamptz(6)
  updatedAt             DateTime @updatedAt @map("updated_at") @db.Timestamptz(6)
  userId                String   @unique @map("user_id") @db.Uuid
  user                  User     @relation(fields: [userId], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "user_key_bundles_user_id_e121d3b4_fk_users_id")

  @@map("user_key_bundles")
}

model OneTimePreKey {
  id        String    @id @default(uuid()) @db.Uuid
  keyId     Int       @map("key_id")
  publicKey String    @map("public_key")
  isUsed    Boolean   @map("is_used")
  createdAt DateTime  @map("created_at") @db.Timestamptz(6)
  usedAt    DateTime? @map("used_at") @db.Timestamptz(6)
  userId    String    @map("user_id") @db.Uuid
  user      User      @relation(fields: [userId], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "one_time_prekeys_user_id_c3428282_fk_users_id")

  @@unique([userId, keyId], map: "one_time_prekeys_user_id_key_id_6c0070f0_uniq")
  @@index([createdAt], map: "one_time_pr_created_1f3399_idx")
  @@index([userId, isUsed], map: "one_time_pr_user_id_565628_idx")
  @@index([userId], map: "one_time_prekeys_user_id_c3428282")
  @@map("one_time_prekeys")
}

model ConversationSession {
  id                   String       @id @default(uuid()) @db.Uuid
  sessionState         Json         @map("session_state")
  rootKey              String       @map("root_key")
  chainKeySend         String?      @map("chain_key_send")
  chainKeyReceive      String?      @map("chain_key_receive")
  messageNumberSend    Int          @map("message_number_send")
  messageNumberReceive Int          @map("message_number_receive")
  previousChainLength  Int          @map("previous_chain_length")
  createdAt            DateTime     @map("created_at") @db.Timestamptz(6)
  updatedAt            DateTime     @updatedAt @map("updated_at") @db.Timestamptz(6)
  conversationId       String       @map("conversation_id") @db.Uuid
  participantId        String       @map("participant_id") @db.Uuid
  conversation         Conversation @relation(fields: [conversationId], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "conversation_session_conversation_id_f7d9cbcd_fk_conversat")
  participant          User         @relation(fields: [participantId], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "conversation_sessions_participant_id_c8e379f0_fk_users_id")
  messageKeys          MessageKey[]

  @@unique([conversationId, participantId], map: "conversation_sessions_conversation_id_particip_1012dfaa_uniq")
  @@index([conversationId, participantId], map: "conversatio_convers_170dde_idx")
  @@index([updatedAt], map: "conversatio_updated_2cfa54_idx")
  @@index([conversationId], map: "conversation_sessions_conversation_id_f7d9cbcd")
  @@index([participantId], map: "conversation_sessions_participant_id_c8e379f0")
  @@map("conversation_sessions")
}

model MessageKey {
  id               String              @id @default(uuid()) @db.Uuid
  messageNumber    Int                 @map("message_number")
  messageKey       String              @map("message_key")
  senderRatchetKey String              @map("sender_ratchet_key")
  createdAt        DateTime            @map("created_at") @db.Timestamptz(6)
  sessionId        String              @map("session_id") @db.Uuid
  session          ConversationSession @relation(fields: [sessionId], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "message_keys_session_id_7de76111_fk_conversation_sessions_id")

  @@unique([sessionId, senderRatchetKey, messageNumber], map: "message_keys_session_id_sender_ratche_bffd6856_uniq")
  @@index([createdAt], map: "message_key_created_fd0ccb_idx")
  @@index([sessionId, messageNumber], map: "message_key_session_78960f_idx")
  @@index([sessionId], map: "message_keys_session_id_7de76111")
  @@map("message_keys")
}

model KeyBundleUploadLog {
  id           String   @id @default(uuid()) @db.Uuid
  ipAddress    String   @map("ip_address") @db.Inet
  success      Boolean
  errorMessage String   @map("error_message")
  createdAt    DateTime @map("created_at") @db.Timestamptz(6)
  userId       String   @map("user_id") @db.Uuid
  user         User     @relation(fields: [userId], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "key_bundle_upload_logs_user_id_9187c577_fk_users_id")

  @@index([ipAddress, createdAt], map: "key_bundle__ip_addr_d36791_idx")
  @@index([success, createdAt], map: "key_bundle__success_b70a97_idx")
  @@index([userId, createdAt], map: "key_bundle__user_id_de82a1_idx")
  @@index([userId], map: "key_bundle_upload_logs_user_id_9187c577")
  @@map("key_bundle_upload_logs")
}

model auth_group {
  id                     Int                      @id @default(autoincrement())
  name                   String                   @unique @db.VarChar(150)
  auth_group_permissions auth_group_permissions[]
  users_groups           users_groups[]

  @@index([name], map: "auth_group_name_a6ea08ec_like")
}

/// This model has constraints using non-default deferring rules and requires additional setup for migrations. Visit https://pris.ly/d/constraint-deferring for more info.
model auth_group_permissions {
  id              BigInt          @id @default(autoincrement())
  group_id        Int
  permission_id   Int
  auth_permission auth_permission @relation(fields: [permission_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "auth_group_permissio_permission_id_84c5c92e_fk_auth_perm")
  auth_group      auth_group      @relation(fields: [group_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "auth_group_permissions_group_id_b120cbf9_fk_auth_group_id")

  @@unique([group_id, permission_id], map: "auth_group_permissions_group_id_permission_id_0cd325b0_uniq")
  @@index([group_id], map: "auth_group_permissions_group_id_b120cbf9")
  @@index([permission_id], map: "auth_group_permissions_permission_id_84c5c92e")
}

/// This model has constraints using non-default deferring rules and requires additional setup for migrations. Visit https://pris.ly/d/constraint-deferring for more info.
model auth_permission {
  id                     Int                      @id @default(autoincrement())
  name                   String                   @db.VarChar(255)
  content_type_id        Int
  codename               String                   @db.VarChar(100)
  auth_group_permissions auth_group_permissions[]
  django_content_type    django_content_type      @relation(fields: [content_type_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "auth_permission_content_type_id_2f476e4b_fk_django_co")
  users_user_permissions users_user_permissions[]

  @@unique([content_type_id, codename], map: "auth_permission_content_type_id_codename_01ab375a_uniq")
  @@index([content_type_id], map: "auth_permission_content_type_id_2f476e4b")
}

/// This table contains check constraints and requires additional setup for migrations. Visit https://pris.ly/d/check-constraints for more info.
/// This model has constraints using non-default deferring rules and requires additional setup for migrations. Visit https://pris.ly/d/constraint-deferring for more info.
model django_admin_log {
  id                  Int                  @id @default(autoincrement())
  action_time         DateTime             @db.Timestamptz(6)
  object_id           String?
  object_repr         String               @db.VarChar(200)
  action_flag         Int                  @db.SmallInt
  change_message      String
  content_type_id     Int?
  user_id             String               @db.Uuid
  django_content_type django_content_type? @relation(fields: [content_type_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "django_admin_log_content_type_id_c4bce8eb_fk_django_co")
  users               User                 @relation(fields: [user_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "django_admin_log_user_id_c564eba6_fk_users_id")

  @@index([content_type_id], map: "django_admin_log_content_type_id_c4bce8eb")
  @@index([user_id], map: "django_admin_log_user_id_c564eba6")
}

model django_content_type {
  id               Int                @id @default(autoincrement())
  app_label        String             @db.VarChar(100)
  model            String             @db.VarChar(100)
  auth_permission  auth_permission[]
  django_admin_log django_admin_log[]

  @@unique([app_label, model], map: "django_content_type_app_label_model_76bd3d3b_uniq")
}

model django_migrations {
  id      BigInt   @id @default(autoincrement())
  app     String   @db.VarChar(255)
  name    String   @db.VarChar(255)
  applied DateTime @db.Timestamptz(6)
}

model django_session {
  session_key  String   @id @db.VarChar(40)
  session_data String
  expire_date  DateTime @db.Timestamptz(6)

  @@index([expire_date], map: "django_session_expire_date_a5c62663")
  @@index([session_key], map: "django_session_session_key_c0390e0f_like")
}

/// This model has constraints using non-default deferring rules and requires additional setup for migrations. Visit https://pris.ly/d/constraint-deferring for more info.
model users_groups {
  id         BigInt     @id @default(autoincrement())
  user_id    String     @db.Uuid
  group_id   Int
  auth_group auth_group @relation(fields: [group_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "users_groups_group_id_2f3517aa_fk_auth_group_id")
  users      User       @relation(fields: [user_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "users_groups_user_id_f500bee5_fk_users_id")

  @@unique([user_id, group_id], map: "users_groups_user_id_group_id_fc7788e8_uniq")
  @@index([group_id], map: "users_groups_group_id_2f3517aa")
  @@index([user_id], map: "users_groups_user_id_f500bee5")
}

/// This model has constraints using non-default deferring rules and requires additional setup for migrations. Visit https://pris.ly/d/constraint-deferring for more info.
model users_user_permissions {
  id              BigInt          @id @default(autoincrement())
  user_id         String          @db.Uuid
  permission_id   Int
  auth_permission auth_permission @relation(fields: [permission_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "users_user_permissio_permission_id_6d08dcd2_fk_auth_perm")
  users           User            @relation(fields: [user_id], references: [id], onDelete: NoAction, onUpdate: NoAction, map: "users_user_permissions_user_id_92473840_fk_users_id")

  @@unique([user_id, permission_id], map: "users_user_permissions_user_id_permission_id_3b86cbdf_uniq")
  @@index([permission_id], map: "users_user_permissions_permission_id_6d08dcd2")
  @@index([user_id], map: "users_user_permissions_user_id_92473840")
}
