// socket-server/src/events/socketEvents.ts
import { Server, Socket } from 'socket.io';
import { MessageService } from '../services/messageService';
import { ConversationService } from '../services/conversationService';
import { MessageStatusService } from '../services/messageStatusService';
import { z } from 'zod';
import {
  MessageCreateSchema,
  JoinRoomSchema,
  TypingEventSchema,
  MessageStatusInputSchema
} from '../schemas';
import {
  SendMessageSchema,
  TypingIndicatorSchema,
  JoinConversationSchema,
  LeaveConversationSchema,
  KeyExchangeRequestSchema,
  KeyExchangeResponseSchema,
  validateSocketEvent,
  createSocketError,
  isEncryptedMessage,
  isPlaintextMessage,
  type SendMessage,
  type TypingIndicator,
  type KeyExchangeRequest,
  type KeyExchangeResponse,
} from '../schemas/encryptionSchemas';
import { EncryptionService } from '../services/encryptionService';

interface AuthenticatedSocket extends Socket {
  userId: string;
  user: any;
}

export class SocketEventHandler {
  private io: Server;
  private messageService: MessageService;
  private conversationService: ConversationService;
  private messageStatusService: MessageStatusService;
  private encryptionService: EncryptionService;

  constructor(
    io: Server,
    messageService: MessageService,
    conversationService: ConversationService,
    messageStatusService: MessageStatusService,
    encryptionService: EncryptionService
  ) {
    this.io = io;
    this.messageService = messageService;
    this.conversationService = conversationService;
    this.messageStatusService = messageStatusService;
    this.encryptionService = encryptionService;
  }

  handleConnection(socket: AuthenticatedSocket) {
    console.log(`User ${socket.userId} connected to messaging`);

    // Join user to their conversations
    socket.on('join_conversations', () => this.handleJoinConversations(socket));

    // Handle new message creation (supports both encrypted and plaintext)
    socket.on('send_message', (data) => this.handleSendMessage(socket, data));

    // Handle typing indicators
    socket.on('typing_start', (data) => this.handleTypingStart(socket, data));
    socket.on('typing_stop', (data) => this.handleTypingStop(socket, data));

    // Phase 3: Handle encryption-specific events
    socket.on('key_exchange_request', (data) => this.handleKeyExchangeRequest(socket, data));
    socket.on('encryption_status_check', (data) => this.handleEncryptionStatusCheck(socket, data));

    // Handle joining specific conversation room
    socket.on('join_conversation', (data) => this.handleJoinConversation(socket, data));

    // Handle user status
    socket.on('user_online', () => this.handleUserOnline(socket));

    // Handle message status updates
    socket.on('message_delivered', (data) => this.handleMessageDelivered(socket, data));
    socket.on('message_read', (data) => this.handleMessageRead(socket, data));
    socket.on('message_failed', (data) => this.handleMessageFailed(socket, data));

    // Handle disconnection
    socket.on('disconnect', () => this.handleDisconnect(socket));
  }

  private async handleJoinConversations(socket: AuthenticatedSocket) {
    try {
      const conversations = await this.conversationService.getUserConversations(socket.userId);

      // Join all conversation rooms
      for (const conversation of conversations) {
        socket.join(`conversation_${conversation.id}`);
      }

      socket.emit('conversations_joined', {
        success: true,
        count: conversations.length,
        conversations: conversations
      });
    } catch (error) {
      console.error('Error joining conversations:', error);
      socket.emit('error', { message: 'Failed to join conversations' });
    }
  }

  private async handleSendMessage(socket: AuthenticatedSocket, data: any) {
    try {
      // Validate message data (supports both encrypted and plaintext)
      const validatedData = validateSocketEvent(SendMessageSchema, data);

      // Check if this is an encrypted message
      const isEncrypted = isEncryptedMessage(validatedData);

      // Log message type (without content for security)
      console.log(`User ${socket.userId} sending ${isEncrypted ? 'encrypted' : 'plaintext'} message to conversation ${validatedData.conversationId}`);

      // Create message in database
      const message = await this.messageService.createMessage(validatedData, socket.userId);

      // Create DELIVERED status for the sender
      await this.messageStatusService.markMessageAsDelivered(message.id, socket.userId);

      // Prepare message payload for broadcasting
      const messagePayload = {
        id: message.id,
        conversationId: message.conversationId,
        sender: message.sender,
        messageType: message.messageType,
        createdAt: message.createdAt,
        updatedAt: message.updatedAt,
        status: 'DELIVERED',
        // Include appropriate content based on message type
        ...(isEncrypted ? {
          // For encrypted messages, include encrypted fields
          encryptedContent: message.encryptedContent,
          iv: message.iv,
          senderRatchetKey: message.senderRatchetKey,
          messageNumber: message.messageNumber,
          previousChainLength: message.previousChainLength,
          isEncrypted: true,
        } : {
          // For plaintext messages, include content
          content: message.content,
          isEncrypted: false,
        })
      };

      // Emit to all participants in the conversation
      this.io.to(`conversation_${message.conversationId}`).emit('new_message', messagePayload);

      // Acknowledge to sender
      socket.emit('message_sent', {
        tempId: data.tempId, // For optimistic UI updates
        messageId: message.id,
        status: 'DELIVERED',
        isEncrypted
      });

      // Log successful message delivery (without sensitive content)
      console.log(`Message ${message.id} delivered successfully (encrypted: ${isEncrypted})`);

    } catch (error) {
      console.error('Error sending message:', error);

      // Mark message as failed if it exists
      if (data.tempId) {
        socket.emit('message_failed', {
          tempId: data.tempId,
          error: error instanceof Error ? error.message : 'Failed to send message'
        });
      }

      if (error instanceof z.ZodError) {
        socket.emit('error', createSocketError(
          'Invalid message data',
          'VALIDATION_ERROR',
          { issues: error.issues }
        ));
      } else {
        socket.emit('error', createSocketError('Failed to send message', 'MESSAGE_SEND_ERROR'));
      }
    }
  }

  private async handleTypingStart(socket: AuthenticatedSocket, data: any) {
    try {
      const typingData = TypingEventSchema.parse(data);

      // Verify access to conversation
      const hasAccess = await this.messageService.verifyConversationAccess(
        socket.userId,
        typingData.conversationId
      );

      if (hasAccess) {
        socket.to(`conversation_${typingData.conversationId}`).emit('user_typing', {
          userId: socket.userId,
          conversationId: typingData.conversationId,
          isTyping: true
        });
      }
    } catch (error) {
      console.error('Error handling typing start:', error);
    }
  }

  private async handleTypingStop(socket: AuthenticatedSocket, data: any) {
    try {
      const typingData = TypingEventSchema.parse(data);

      const hasAccess = await this.messageService.verifyConversationAccess(
        socket.userId,
        typingData.conversationId
      );

      if (hasAccess) {
        socket.to(`conversation_${typingData.conversationId}`).emit('user_typing', {
          userId: socket.userId,
          conversationId: typingData.conversationId,
          isTyping: false
        });
      }
    } catch (error) {
      console.error('Error handling typing stop:', error);
    }
  }

  private async handleJoinConversation(socket: AuthenticatedSocket, data: any) {
    try {
      const { conversationId } = JoinRoomSchema.parse(data);

      const hasAccess = await this.conversationService.joinConversation(socket.userId, conversationId);
      if (hasAccess) {
        socket.join(`conversation_${conversationId}`);
        socket.emit('joined_conversation', { conversationId });
      } else {
        socket.emit('error', { message: 'Access denied to conversation' });
      }
    } catch (error) {
      console.error('Error joining conversation:', error);
      socket.emit('error', { message: 'Failed to join conversation' });
    }
  }

  private async handleUserOnline(socket: AuthenticatedSocket) {
    try {
      await this.messageService.updateUserStatus(socket.userId, true);

      socket.broadcast.emit('user_status_change', {
        userId: socket.userId,
        status: 'online'
      });
    } catch (error) {
      console.error('Error updating user online status:', error);
    }
  }

  private async handleDisconnect(socket: AuthenticatedSocket) {
    console.log(`User ${socket.userId} disconnected from messaging`);

    try {
      await this.messageService.updateUserStatus(socket.userId, false);

      socket.broadcast.emit('user_status_change', {
        userId: socket.userId,
        status: 'offline'
      });
    } catch (error) {
      console.error('Error updating user offline status:', error);
    }
  }

  private async handleMessageDelivered(socket: AuthenticatedSocket, data: any) {
    try {
      const validatedData = MessageStatusInputSchema.parse(data);

      const messageStatus = await this.messageStatusService.markMessageAsDelivered(
        validatedData.messageId,
        socket.userId
      );

      // Emit to the message sender
      const message = await this.messageService.getMessageById(validatedData.messageId);
      if (message) {
        this.io.to(`conversation_${message.conversationId}`).emit('message_status_updated', {
          messageId: validatedData.messageId,
          userId: socket.userId,
          status: 'DELIVERED',
          tempId: validatedData.tempId,
          updatedAt: messageStatus.updatedAt
        });
      }
    } catch (error) {
      console.error('Error handling message delivered:', error);
      socket.emit('error', {
        message: 'Failed to update message status',
        details: error instanceof z.ZodError ? error.issues : undefined
      });
    }
  }

  private async handleMessageRead(socket: AuthenticatedSocket, data: any) {
    try {
      const validatedData = MessageStatusInputSchema.parse(data);

      const messageStatus = await this.messageStatusService.markMessageAsRead(
        validatedData.messageId,
        socket.userId
      );

      // Emit to the message sender
      const message = await this.messageService.getMessageById(validatedData.messageId);
      if (message) {
        this.io.to(`conversation_${message.conversationId}`).emit('message_status_updated', {
          messageId: validatedData.messageId,
          userId: socket.userId,
          status: 'READ',
          tempId: validatedData.tempId,
          updatedAt: messageStatus.updatedAt
        });
      }
    } catch (error) {
      console.error('Error handling message read:', error);
      socket.emit('error', {
        message: 'Failed to update message status',
        details: error instanceof z.ZodError ? error.issues : undefined
      });
    }
  }

  private async handleMessageFailed(socket: AuthenticatedSocket, data: any) {
    try {
      const validatedData = MessageStatusInputSchema.parse(data);

      const messageStatus = await this.messageStatusService.markMessageAsFailed(
        validatedData.messageId,
        socket.userId
      );

      // Emit back to the sender
      socket.emit('message_status_updated', {
        messageId: validatedData.messageId,
        userId: socket.userId,
        status: 'FAILED',
        tempId: validatedData.tempId,
        updatedAt: messageStatus.updatedAt
      });
    } catch (error) {
      console.error('Error handling message failed:', error);
      socket.emit('error', {
        message: 'Failed to update message status',
        details: error instanceof z.ZodError ? error.issues : undefined
      });
    }
  }

  // Phase 3: Encryption-specific event handlers

  /**
   * Handle key exchange request between users
   */
  private async handleKeyExchangeRequest(socket: AuthenticatedSocket, data: any) {
    try {
      const validatedData = validateSocketEvent(KeyExchangeRequestSchema, data);

      console.log(`Key exchange request from ${socket.userId} for user ${validatedData.targetUserId}`);

      // Get target user's key bundle
      const keyBundle = await this.encryptionService.getUserKeyBundle(
        validatedData.targetUserId,
        socket.userId
      );

      if (!keyBundle) {
        socket.emit('key_exchange_response', createSocketError(
          'Target user does not have encryption enabled',
          'NO_KEY_BUNDLE'
        ));
        return;
      }

      // Send key bundle back to requester
      const response: KeyExchangeResponse = {
        success: true,
        keyBundle,
      };

      socket.emit('key_exchange_response', response);

      console.log(`Key bundle sent to ${socket.userId} for user ${validatedData.targetUserId}`);

    } catch (error) {
      console.error('Error handling key exchange request:', error);
      socket.emit('key_exchange_response', createSocketError(
        'Failed to process key exchange request',
        'KEY_EXCHANGE_ERROR'
      ));
    }
  }

  /**
   * Check encryption status for a conversation
   */
  private async handleEncryptionStatusCheck(socket: AuthenticatedSocket, data: any) {
    try {
      const { conversationId } = data;

      if (!conversationId) {
        socket.emit('encryption_status_response', createSocketError(
          'Conversation ID required',
          'MISSING_CONVERSATION_ID'
        ));
        return;
      }

      // Check if conversation supports encryption
      const isEncrypted = await this.encryptionService.isConversationEncrypted(conversationId);
      const participants = await this.encryptionService.getConversationParticipants(conversationId);

      socket.emit('encryption_status_response', {
        conversationId,
        isEncrypted,
        participants: participants.map(p => ({
          id: p.id,
          username: p.username,
          hasEncryption: p.hasEncryption,
        })),
      });

    } catch (error) {
      console.error('Error checking encryption status:', error);
      socket.emit('encryption_status_response', createSocketError(
        'Failed to check encryption status',
        'ENCRYPTION_STATUS_ERROR'
      ));
    }
  }
}
